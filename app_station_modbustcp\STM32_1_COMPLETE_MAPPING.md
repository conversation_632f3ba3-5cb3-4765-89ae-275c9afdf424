# STM32[1]完整数据映射方案

## 新的映射策略

将STM32[1]的完整32位数据映射到寄存器0和寄存器1中，这样泵可以获取完整的数据。

### 数据映射规则

```c
// STM32[1]的32位数据拆分
Register[0] = STM32[1] >> 16;     // 高16位
Register[1] = STM32[1] & 0xFFFF;  // 低16位
```

### 具体示例

假设STM32[1] = `0x00012C15`：

```
STM32[1] = 0x00012C15
         = 0001 0010 1100 0001 0101 (二进制)
         
Register[0] = 0x0001  (高16位)
Register[1] = 0x2C15  (低16位)
```

泵读取到：`Register Values: 0x0001 0x2C15`

## 数据重构

泵端可以通过以下方式重构完整的32位数据：

```c
// 泵端重构STM32[1]的完整值
uint32_t complete_data = ((uint32_t)register[0] << 16) | register[1];
// 结果: complete_data = 0x00012C15
```

## 预期调试输出

### 编译烧录后的输出

```
=== CUSTOM DATA MAPPING ===
STM32[0]=0x0000300A, STM32[1]=0x00012C15
Mapping STM32[1] to registers:
  STM32[1] high 16 bits: 0x0001 -> Register[0]
  STM32[1] low 16 bits:  0x2C15 -> Register[1]
Set Register[0] = 0x0001 (STM32[1] high)
Set Register[1] = 0x2C15 (STM32[1] low)
Complete STM32[1] value: 0x00012C15

=== FINAL REGISTER CHECK ===
Register[0] = 0x0001 (STM32[1] high 16 bits)
Register[1] = 0x2C15 (STM32[1] low 16 bits)
✓ STM32[1] mapping SUCCESS: 0x00012C15
Reconstructed STM32[1]: 0x00012C15 (original: 0x00012C15)
```

### 泵读取结果

```
=== ESP8266 RESPONSE ===
Register Values: 0x0001 0x2C15
```

## 数据变化示例

基于之前的日志，预期的数据变化：

```
Update #1: STM32[1]=0x00012B15 -> Reg[0]=0x0001, Reg[1]=0x2B15
Update #2: STM32[1]=0x00012A15 -> Reg[0]=0x0001, Reg[1]=0x2A15
Update #3: STM32[1]=0x00012E15 -> Reg[0]=0x0001, Reg[1]=0x2E15
Update #4: STM32[1]=0x00013115 -> Reg[0]=0x0001, Reg[1]=0x3115
Update #5: STM32[1]=0x00012D15 -> Reg[0]=0x0001, Reg[1]=0x2D15
Update #6: STM32[1]=0x00012C15 -> Reg[0]=0x0001, Reg[1]=0x2C15
```

## 优势

### 1. 完整数据传输
- 泵现在可以获取STM32[1]的完整32位数据
- 不会丢失高16位的信息

### 2. 灵活的数据解析
泵可以选择：
- 只使用低16位：`register[1]`
- 只使用高16位：`register[0]`
- 使用完整32位：`(register[0] << 16) | register[1]`

### 3. 保持兼容性
- 泵仍然只需要读取寄存器0-1
- 不需要修改泵的Modbus请求

## 数据含义分析

### 高16位 (Register[0])
从之前的数据看，高16位通常是`0x0001`，可能表示：
- 数据类型标识
- 传感器ID
- 状态标志
- 数据版本

### 低16位 (Register[1])
低16位在变化（如`0x2C15`），可能表示：
- 实际传感器读数
- 测量值
- 计数器值

### 完整32位值的含义
`0x00012C15`可能表示：
- 传感器类型1的读数为0x2C15
- 或者是一个完整的20位数据值（0x12C15）加上类型标识

## 验证步骤

### 步骤1: 编译烧录
```bash
make clean && make && make flash
```

### 步骤2: 观察调试输出
确认看到：
```
Mapping STM32[1] to registers:
  STM32[1] high 16 bits: 0x0001 -> Register[0]
  STM32[1] low 16 bits:  0x2C15 -> Register[1]
```

### 步骤3: 验证泵读取
泵应该读取到：
```
Register Values: 0x0001 0x2C15
```

而不是之前的单一低16位值。

### 步骤4: 数据重构测试
验证重构的32位值是否与原始STM32[1]值匹配。

## 成功标志

当看到以下输出时，说明映射成功：

```
✓ STM32[1] mapping SUCCESS: 0x00012C15
Reconstructed STM32[1]: 0x00012C15 (original: 0x00012C15)

=== ESP8266 RESPONSE ===
Register Values: 0x0001 0x2C15
```

这表明泵现在能够获取STM32[1]的完整32位数据。
