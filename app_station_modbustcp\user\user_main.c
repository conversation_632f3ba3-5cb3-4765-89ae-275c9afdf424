/*
 * ESPRSSIF MIT License
 *
 * Copyright (c) 2015 <ESPRESSIF SYSTEMS (SHANGHAI) PTE LTD>
 *
 * Permission is hereby granted for use on ESPRESSIF SYSTEMS ESP8266 only, in which case,
 * it is free of charge, to any person obtaining a copy of this software and associated
 * documentation files (the "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the Software is furnished
 * to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all copies or
 * substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
 * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WH<PERSON>HER
 * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 *
 */
#include "ets_sys.h"
#include "os_type.h"
#include "osapi.h"
#include "spi_test.h"
#include "at_custom.h"
#include "user_interface.h"
#include "driver/uart.h"
#include "driver/spi_interface.h"
#include "espconn.h"
#include "mem.h"
#include "user_json.h"
#include "user_devicefind.h"
#include "eagle_soc.h"
#include "spi_flash.h"
#include "modbus_tcp.h"

LOCAL os_timer_t test_timer;
LOCAL os_timer_t wifi_check_timer;
struct espconn PhoneConn;
esp_tcp PhoneConnTcp;
LOCAL struct espconn user_tcp_espconn;
LOCAL struct espconn ptrespconn;
const char *ESP8266_MSG = "I'm ESP8266 ";
const char *device_find_request = "Are You ESP8266 Device?";

const char *device_find_response_ok = "Yes,I'm ESP8266!";

uint8 tcpTx_buf0[8292];
uint8 *tcpTx_buf_pionter;
char tcpRx_buf[32];
uint8 tcpTxCmd_buf[32];
uint8 tcpDataNeedSend = 0;
uint16 tcpWait2SendBytes = 0;
uint8 tst_tcpCnt0 = 0;
uint8 tst_tcpCnt1 = 0;
#define TCP_LENGTH 1448 // TCP 一帧数据长度1448字节

#define SEC 124           //读写的扇区（Sector）号
#define SEC_OFFSET 0//扇区内偏移量（必须是4的倍数）
#define FLASH_LEN 16//以读写16*4字节的数据为例（64字节用于存储SSID和密码）
uint8 flash_write_data[FLASH_LEN*4];
uint8 flash_read_data[FLASH_LEN*4];
uint8 ssid_buf[32] = "USR-W610_A018";     // 泵的WiFi SSID
uint8 password_buf[32] = ""; // 泵的WiFi密码（无密码）
uint8 debugModeOpen = 0;
uint32 version = 20190117;
uint8 wifi_connected = 0;  // WiFi连接状态标志

// 函数声明
void ICACHE_FLASH_ATTR user_tcp_init(void);
void ICACHE_FLASH_ATTR user_set_station_config(void);
LOCAL void ICACHE_FLASH_ATTR wifi_check_ip(void *arg);
void ICACHE_FLASH_ATTR update_modbus_from_stm32_data(uint32_t *data, uint16 length);
void ICACHE_FLASH_ATTR send_modbus_data_to_stm32(uint16 start_addr, uint16 quantity);
void ICACHE_FLASH_ATTR test_modbus_data_update(void);
void ICACHE_FLASH_ATTR test_data_timer_cb(void *arg);
void ICACHE_FLASH_ATTR data_monitor_timer_cb(void *arg);
void ICACHE_FLASH_ATTR test_specific_response(void);
void ICACHE_FLASH_ATTR test_response_timer_cb(void *arg);

// 全局变量用于检测数据变化
static uint32_t last_stm32_data[8] = {0};
static uint32_t data_update_count = 0;
static uint32_t last_data_update_count = 0;

void saveData2Flash() {
	uint8 i = 0;
	uint8 j = 0;
//	os_printf("flash start");
	for(i = 0; i < 32; i++) {
		flash_write_data[j++] = ssid_buf[i];
	}
	for(i = 0; i < 32; i++) {
		flash_write_data[j++] = password_buf[i];
	}

	//写入数据
	spi_flash_erase_sector(SEC);
	spi_flash_write(SEC*4*1024+SEC_OFFSET, (uint32 *)&flash_write_data, FLASH_LEN*4);
	spi_flash_read(SEC*4*1024+SEC_OFFSET, (uint32 *)&flash_read_data, FLASH_LEN*4);
	for(i=0;i<64;i++){
	os_printf("flash[%d]%c ", i,flash_read_data[i]);
	}
}
uint8 readFlash_SsidPassword() {
	uint8 i = 0;
	uint8 j = 0;
	spi_flash_read(SEC*4*1024+SEC_OFFSET, (uint32 *)&flash_read_data, FLASH_LEN*4);
	for(i = 0; i < 32; i++) {
		ssid_buf[i] = flash_read_data[j++];
//		os_printf("s%d",ssid_buf[i]);
	}
	for(i = 0; i < 32; i++) {
		password_buf[i] = flash_read_data[j++];
//		os_printf("p%d",password_buf[i]);
	}
	if((ssid_buf[0] != 'q') || (ssid_buf[1] != 'w')) {
//		os_printf("return 1 ssid_buf[0]%d ",ssid_buf[0]);
		return 0x01;
	}
//	os_printf("return 0 ssid_buf[0]%d ",ssid_buf[0]);
	return 0;
}

/******************************************************************************
 * FunctionName : update_modbus_from_stm32_data
 * Description  : 将STM32数据更新到Modbus寄存器
 * Parameters   : data -- STM32数据
 *                length -- 数据长度
 * Returns      : none
*******************************************************************************/
void ICACHE_FLASH_ATTR
update_modbus_from_stm32_data(uint32_t *data, uint16 length)
{
	uint16 i;
	uint16 reg_addr = 0; // 从寄存器地址0开始映射
	uint8 data_changed = 0;

	// 检测数据是否发生变化
	for(i = 0; i < length && i < 8; i++) {
		if(last_stm32_data[i] != data[i]) {
			data_changed = 1;
			last_stm32_data[i] = data[i];
		}
	}

	if(data_changed) {
		data_update_count++;
		os_printf("=== STM32 DATA CHANGED (Update #%d) ===\n", data_update_count);
	} else {
		os_printf("=== STM32 DATA UNCHANGED ===\n");
	}

	// 简化的自定义映射：直接使用STM32[1]的低16位
	if (length >= 2) {
		// 从STM32[1]=0x00017114中提取低16位数据
		// STM32[1]的低16位 0x7114 -> Register[0]
		// Register[1] 保持 0x0000

		uint32 stm32_data = data[1]; // 例如: 0x00017114

		// 直接取低16位作为寄存器0的值
		uint16 raw_data = (uint16)(stm32_data & 0xFFFF); // 例如: 0x2027
		uint16 reg0_data = raw_data; // 先不做字节序转换，看看实际情况

		// 寄存器1保持0x0000
		uint16 reg1_data = 0x0000;

		os_printf("=== BYTE ORDER ANALYSIS ===\n");
		os_printf("STM32[0]=0x%08X, STM32[1]=0x%08X\n", data[0], data[1]);
		os_printf("STM32[1] breakdown:\n");
		os_printf("  Full 32-bit:  0x%08X\n", stm32_data);
		os_printf("  Low 16 bits:  0x%04X\n", raw_data);
		os_printf("  High byte:    0x%02X\n", (raw_data >> 8) & 0xFF);
		os_printf("  Low byte:     0x%02X\n", raw_data & 0xFF);
		os_printf("  Byte-swapped: 0x%02X%02X\n", raw_data & 0xFF, (raw_data >> 8) & 0xFF);

		os_printf("Register mapping:\n");
		os_printf("  Register[0] = 0x%04X (no byte swap)\n", reg0_data);
		os_printf("  Register[1] = 0x%04X (fixed value)\n", reg1_data);

		// 添加字节序转换选项
		uint16 swapped_data = ((raw_data & 0xFF) << 8) | ((raw_data >> 8) & 0xFF);
		os_printf("Byte order options:\n");
		os_printf("  Option 1 (no swap):    0x%04X\n", raw_data);
		os_printf("  Option 2 (byte swap):  0x%04X\n", swapped_data);

		// 设置寄存器
		modbus_set_input_register(0, reg0_data);
		modbus_set_holding_register(0, reg0_data);
		modbus_set_input_register(1, reg1_data);
		modbus_set_holding_register(1, reg1_data);

		os_printf("Set Register[0] = 0x%04X\n", reg0_data);
		os_printf("Set Register[1] = 0x%04X\n", reg1_data);
		os_printf("Expected Raw Response data: %02X %02X %02X %02X\n",
			(reg0_data >> 8) & 0xFF, reg0_data & 0xFF,
			(reg1_data >> 8) & 0xFF, reg1_data & 0xFF);
	}

	// 将所有STM32数据映射到寄存器2及以后（完整映射，用于调试）
	reg_addr = 2;
	for (i = 0; i < length && reg_addr < 500; i++) {
		// 高16位
		modbus_set_input_register(reg_addr, (uint16)(data[i] >> 16));
		modbus_set_holding_register(reg_addr, (uint16)(data[i] >> 16));
		reg_addr++;

		// 低16位
		if (reg_addr < 500) {
			modbus_set_input_register(reg_addr, (uint16)(data[i] & 0xFFFF));
			modbus_set_holding_register(reg_addr, (uint16)(data[i] & 0xFFFF));
			reg_addr++;
		}
	}

	// 始终打印STM32数据更新信息，不受debugModeOpen控制
	os_printf("=== STM32 DATA UPDATE ===\n");
	os_printf("Updated %d Modbus registers from STM32 data\n", reg_addr);
	os_printf("=== FINAL REGISTER CHECK ===\n");
	os_printf("Register[0] = 0x%04X (STM32[1] low 16 bits)\n", modbus_get_holding_register(0));
	os_printf("Register[1] = 0x%04X (fixed to 0x0000)\n", modbus_get_holding_register(1));

	// 验证数据是否正确写入
	if (length >= 2) {
		uint16 expected_reg0 = (uint16)(data[1] & 0xFFFF); // STM32[1]的低16位
		uint16 expected_reg1 = 0x0000; // 固定为0
		uint16 actual_reg0 = modbus_get_holding_register(0);
		uint16 actual_reg1 = modbus_get_holding_register(1);

		if (expected_reg0 == actual_reg0 && expected_reg1 == actual_reg1) {
			os_printf("✓ Simplified mapping SUCCESS\n");
		} else {
			os_printf("✗ Simplified mapping FAILED:\n");
			os_printf("  Expected: Reg[0]=0x%04X, Reg[1]=0x%04X\n", expected_reg0, expected_reg1);
			os_printf("  Actual:   Reg[0]=0x%04X, Reg[1]=0x%04X\n", actual_reg0, actual_reg1);
		}

		os_printf("Pump will read: Register Values: 0x%04X 0x%04X\n", actual_reg0, actual_reg1);
		os_printf("Raw Response will contain: %02X %02X %02X %02X\n",
			(actual_reg0 >> 8) & 0xFF, actual_reg0 & 0xFF,
			(actual_reg1 >> 8) & 0xFF, actual_reg1 & 0xFF);

		// 检查寄存器1是否为0
		if (actual_reg1 == 0x0000) {
			os_printf("✓ Register[1] correctly set to 0x0000\n");
		} else {
			os_printf("✗ Register[1] should be 0x0000, got 0x%04X\n", actual_reg1);
		}
	}

	// 打印原始32位数据
	os_printf("Raw 32-bit values: ");
	for(i = 0; i < length && i < 8; i++) {
		os_printf("0x%08X ", data[i]);
		if(i % 4 == 3) os_printf("\n                  ");
	}
	os_printf("\n");

	// 打印映射后的16位寄存器值
	os_printf("16-bit registers: ");
	for(i = 0; i < 16 && i < reg_addr; i++) {
		os_printf("0x%04X ", modbus_get_holding_register(i));
		if(i % 8 == 7) os_printf("\n                 ");
	}
	os_printf("\n");

	// 打印寄存器地址映射
	os_printf("Register mapping:\n");
	for(i = 0; i < length && i < 8; i++) {
		os_printf("  STM32[%d]=0x%08X -> Reg[%d]=0x%04X, Reg[%d]=0x%04X\n",
			i, data[i],
			i*2, modbus_get_holding_register(i*2),
			i*2+1, modbus_get_holding_register(i*2+1));
	}
	os_printf("\n");
}

/******************************************************************************
 * FunctionName : send_modbus_data_to_stm32
 * Description  : 将Modbus保持寄存器数据发送到STM32
 * Parameters   : start_addr -- 起始寄存器地址
 *                quantity -- 寄存器数量
 * Returns      : none
*******************************************************************************/
void ICACHE_FLASH_ATTR
send_modbus_data_to_stm32(uint16 start_addr, uint16 quantity)
{
	uint32_t spi_data[8] = {0};
	uint16 i, reg_pairs;
	uint16 high_reg, low_reg;

	// 限制最多发送8个32位数据（16个寄存器）
	reg_pairs = (quantity + 1) / 2;
	if (reg_pairs > 8) {
		reg_pairs = 8;
	}

	// 将连续的两个16位寄存器组合成一个32位数据
	// start_addr现在是相对于控制区域的地址（已经减去了16）
	for (i = 0; i < reg_pairs; i++) {
		high_reg = modbus_get_holding_register(start_addr + 16 + i * 2); // 加回16偏移
		low_reg = (start_addr + i * 2 + 1 < start_addr + quantity) ?
		          modbus_get_holding_register(start_addr + 16 + i * 2 + 1) : 0; // 加回16偏移

		spi_data[i] = ((uint32_t)high_reg << 16) | low_reg;
	}

	// 通过SPI发送数据到STM32
	SPISlaveSendData(SpiNum_HSPI, spi_data, reg_pairs);

	if(debugModeOpen) {
		os_printf("Sent %d Modbus holding registers to STM32 via SPI\n", quantity);
	}
}

/******************************************************************************
 * FunctionName : test_modbus_data_update
 * Description  : 测试函数，模拟STM32数据更新
 * Parameters   : none
 * Returns      : none
*******************************************************************************/
void ICACHE_FLASH_ATTR
test_modbus_data_update(void)
{
	uint32_t test_data[8] = {
		0x11223344, 0x55667788, 0x99AABBCC, 0xDDEEFF00,
		0x12345678, 0x9ABCDEF0, 0x13579BDF, 0x2468ACE0
	};

	os_printf("=== TEST DATA UPDATE ===\n");
	os_printf("Simulating STM32 data update with test values\n");

	// 更新Modbus寄存器
	update_modbus_from_stm32_data(test_data, 8);

	os_printf("Test data update complete\n");
}

/******************************************************************************
 * FunctionName : test_data_timer_cb
 * Description  : 定时器回调函数，定期更新测试数据
 * Parameters   : arg -- 定时器参数
 * Returns      : none
*******************************************************************************/
void ICACHE_FLASH_ATTR
test_data_timer_cb(void *arg)
{
	static uint32 counter = 0;
	uint32_t test_data[8];
	uint8 i;

	counter++;

	// 生成变化的测试数据
	for(i = 0; i < 8; i++) {
		test_data[i] = 0x10000000 + (counter << 16) + (i << 8) + (counter & 0xFF);
	}

	os_printf("=== TIMER DATA UPDATE #%d ===\n", counter);

	// 更新Modbus寄存器
	update_modbus_from_stm32_data(test_data, 8);

	// 显示如果泵请求16个寄存器会得到的完整数据
	os_printf("=== COMPLETE 32-BYTE DATA AVAILABLE ===\n");
	os_printf("If pump requests 16 registers (addr=0, qty=16):\n");
	for(i = 0; i < 16; i += 2) {
		os_printf("  Reg[%2d-%-2d]: 0x%04X 0x%04X -> STM32[%d]=0x%08X\n",
			i, i+1,
			modbus_get_holding_register(i),
			modbus_get_holding_register(i+1),
			i/2,
			(modbus_get_holding_register(i) << 16) | modbus_get_holding_register(i+1));
	}
	os_printf("Total available data: 32 bytes (16 registers)\n");
}

/******************************************************************************
 * FunctionName : data_monitor_timer_cb
 * Description  : 数据监控定时器回调函数，检查STM32数据更新状态
 * Parameters   : arg -- 定时器参数
 * Returns      : none
*******************************************************************************/
void ICACHE_FLASH_ATTR
data_monitor_timer_cb(void *arg)
{
	static uint32 monitor_count = 0;
	monitor_count++;

	os_printf("=== DATA MONITOR #%d ===\n", monitor_count);
	os_printf("STM32 data updates: %d (new: %d)\n",
		data_update_count, data_update_count - last_data_update_count);

	if(data_update_count == last_data_update_count) {
		os_printf("WARNING: No STM32 data updates in last 10 seconds!\n");
		os_printf("Current register values: 0x%04X 0x%04X\n",
			modbus_get_holding_register(0), modbus_get_holding_register(1));
	} else {
		os_printf("STM32 data is updating normally\n");
	}

	last_data_update_count = data_update_count;
}

/******************************************************************************
 * FunctionName : test_specific_response
 * Description  : 测试函数，设置特定的寄存器值以产生期望的响应
 * Parameters   : none
 * Returns      : none
*******************************************************************************/
void ICACHE_FLASH_ATTR
test_specific_response(void)
{
	os_printf("=== TEST SPECIFIC RESPONSE ===\n");
	os_printf("Setting registers to produce response: 01 03 04 02 26 00 00\n");

	// 设置寄存器值
	// 期望响应: 01 03 04 02 26 00 00
	// 寄存器0 = 0x0226
	// 寄存器1 = 0x0000
	modbus_set_input_register(0, 0xffff);
	modbus_set_holding_register(0, 0xffff);
	modbus_set_input_register(1, 0x0000);
	modbus_set_holding_register(1, 0x0000);

	os_printf("Set Register[0] = 0x0226 (decimal: %d)\n", 0x0226);
	os_printf("Set Register[1] = 0x0000 (decimal: %d)\n", 0x0000);

	// 验证设置
	uint16 reg0 = modbus_get_holding_register(0);
	uint16 reg1 = modbus_get_holding_register(1);

	os_printf("Verification:\n");
	os_printf("  Register[0] = 0x%04X\n", reg0);
	os_printf("  Register[1] = 0x%04X\n", reg1);

	// 计算期望的响应字节
	os_printf("Expected Modbus response (without MBAP header):\n");
	os_printf("  Unit ID:     01\n");
	os_printf("  Function:    03\n");
	os_printf("  Byte count:  04\n");
	os_printf("  Reg[0] high: %02X\n", (reg0 >> 8) & 0xFF);
	os_printf("  Reg[0] low:  %02X\n", reg0 & 0xFF);
	os_printf("  Reg[1] high: %02X\n", (reg1 >> 8) & 0xFF);
	os_printf("  Reg[1] low:  %02X\n", reg1 & 0xFF);
	os_printf("Complete response: 01 03 04 %02X %02X %02X %02X\n",
		(reg0 >> 8) & 0xFF, reg0 & 0xFF, (reg1 >> 8) & 0xFF, reg1 & 0xFF);

	if (reg0 == 0x0226 && reg1 == 0x0000) {
		os_printf("✓ Test data set successfully!\n");
		os_printf("✓ Pump should receive: 01 03 04 02 26 00 00\n");
	} else {
		os_printf("✗ Test data setting failed!\n");
	}
}

/******************************************************************************
 * FunctionName : test_response_timer_cb
 * Description  : 定时器回调函数，定期执行特定响应测试
 * Parameters   : arg -- 定时器参数
 * Returns      : none
*******************************************************************************/
void ICACHE_FLASH_ATTR
test_response_timer_cb(void *arg)
{
	static uint32 test_count = 0;
	test_count++;

	os_printf("\n=== PERIODIC TEST DATA UPDATE #%d ===\n", test_count);
	test_specific_response();
	os_printf("Test data refreshed. Next update in 5 seconds...\n");
}

/******************************************************************************
 * FunctionName : update_sensor_data
 * Description  : 更新传感器数据到Modbus寄存器，供泵读取
 * Parameters   : none
 * Returns      : none
*******************************************************************************/
void ICACHE_FLASH_ATTR
update_sensor_data(void)
{
	static uint32 update_counter = 0;
	uint16 sensor_base_addr = 100; // 从地址100开始存储传感器数据

	update_counter++;

	// 模拟传感器数据（实际应用中从STM32获取真实传感器数据）
	// 地址100-109: 基本传感器数据
	modbus_set_input_register(sensor_base_addr + 0, 2500 + (update_counter % 100));  // 温度 (25.00°C + 变化)
	modbus_set_input_register(sensor_base_addr + 1, 6500 + (update_counter % 200));  // 湿度 (65.00% + 变化)
	modbus_set_input_register(sensor_base_addr + 2, 10130 + (update_counter % 50));  // 压力 (101.30kPa + 变化)
	modbus_set_input_register(sensor_base_addr + 3, 750 + (update_counter % 30));    // 流量 (7.50L/min + 变化)
	modbus_set_input_register(sensor_base_addr + 4, 1200 + (update_counter % 100));  // 液位 (12.00cm + 变化)

	// 地址110-119: 状态和报警信息
	modbus_set_input_register(sensor_base_addr + 10, (update_counter % 2));          // 传感器状态 (0=正常, 1=报警)
	modbus_set_input_register(sensor_base_addr + 11, 0);                             // 错误代码
	modbus_set_input_register(sensor_base_addr + 12, update_counter & 0xFFFF);       // 更新计数器
	modbus_set_input_register(sensor_base_addr + 13, version & 0xFFFF);              // 固件版本
	modbus_set_input_register(sensor_base_addr + 14, wifi_connected ? 1 : 0);       // WiFi连接状态

	// 地址120-129: 扩展数据
	modbus_set_input_register(sensor_base_addr + 20, system_get_free_heap_size() >> 16); // 剩余内存高位
	modbus_set_input_register(sensor_base_addr + 21, system_get_free_heap_size() & 0xFFFF); // 剩余内存低位

	if(debugModeOpen && (update_counter % 100 == 0)) {
		os_printf("Sensor data updated (cycle %d)\n", update_counter);
	}
}

/******************************************************************************
 * FunctionName : wifi_check_ip
 * Description  : check WiFi connection status and handle connection events
 * Parameters   : arg -- Additional argument to pass to the callback function
 * Returns      : none
*******************************************************************************/
LOCAL void ICACHE_FLASH_ATTR
wifi_check_ip(void *arg)
{
	struct ip_info ipConfig;
	uint8 wifiStatus;

	// 喂看门狗防止重启
	system_soft_wdt_feed();

	os_timer_disarm(&wifi_check_timer);
	wifi_get_ip_info(STATION_IF, &ipConfig);
	wifiStatus = wifi_station_get_connect_status();

	// 检查WiFi连接状态（对于静态IP，可能不会收到STATION_GOT_IP）
	if ((wifiStatus == STATION_GOT_IP || wifiStatus == STATION_CONNECTING) &&
		(ipConfig.ip.addr != 0 || wifi_station_get_connect_status() == STATION_GOT_IP))
	{
		if (!wifi_connected) {
			wifi_connected = 1;

			// 重新获取IP信息以确保显示正确
			wifi_get_ip_info(STATION_IF, &ipConfig);

			os_printf("WiFi connected to pump! IP: %d.%d.%d.%d\n",
				(ipConfig.ip.addr) & 0xFF,
				(ipConfig.ip.addr >> 8) & 0xFF,
				(ipConfig.ip.addr >> 16) & 0xFF,
				(ipConfig.ip.addr >> 24) & 0xFF);

			// WiFi连接完成，初始化Modbus TCP服务器
			user_tcp_init();

			// 启动数据监控定时器，每10秒检查一次STM32数据更新状态
			os_timer_disarm(&test_timer);
			os_timer_setfn(&test_timer, (os_timer_func_t *)data_monitor_timer_cb, NULL);
			os_timer_arm(&test_timer, 10000, 1); // 10秒间隔，重复执行

			// 开始Modbus TCP服务，等待泵连接
			os_printf("Sensor ready! Pump can connect to ***********:502 for Modbus TCP\n");
			os_printf("Waiting for STM32 data via SPI...\n");
			os_printf("Data monitor will check STM32 updates every 10 seconds\n");
		}

		// 每2秒检查一次连接状态
		os_timer_setfn(&wifi_check_timer, (os_timer_func_t *)wifi_check_ip, NULL);
		os_timer_arm(&wifi_check_timer, 2000, 0);
	}
	else
	{
		wifi_connected = 0;

		if(wifi_station_get_connect_status() == STATION_WRONG_PASSWORD)
		{
			os_printf("STATION_WRONG_PASSWORD\n");
			wifi_station_connect();
		}
		else if(wifi_station_get_connect_status() == STATION_NO_AP_FOUND)
		{
			os_printf("STATION_NO_AP_FOUND\n");
			wifi_station_connect();
		}
		else if(wifi_station_get_connect_status() == STATION_CONNECT_FAIL)
		{
			os_printf("STATION_CONNECT_FAIL\n");
			wifi_station_connect();
		}
		else
		{
			os_printf("STATION_IDLE\n");
		}

		// 每500ms重试连接
		os_timer_setfn(&wifi_check_timer, (os_timer_func_t *)wifi_check_ip, NULL);
		os_timer_arm(&wifi_check_timer, 500, 0);
	}
}

uint32_t tcpRxBuf0[8] = {0};
uint32_t tcpRxBuf1[8] = {0};
uint32_t tcpRxBuf2[8] = {0};
uint32_t tcpRxBuf3[8] = {0};
uint32_t tcpRxBuf4[8] = {0};
uint8 tcpRxCacheCnt = 0; //接收到来自APP的数据包计数

void ICACHE_FLASH_ATTR
modbus_tcp_recv(void *arg, char *data, unsigned short len)
{
	// 只处理Modbus TCP请求，不处理SPI数据
	// SPI数据处理在SPI中断中完成
	modbus_tcp_process_request(arg, data, len);
}

/******************************************************************************
 * FunctionName : user_tcp_connect_cb
 * Description  : tcp connection established callback
 * Parameters   : arg -- Additional argument to pass to the callback function
 * Returns      : none
*******************************************************************************/
LOCAL void ICACHE_FLASH_ATTR
user_tcp_connect_cb(void *arg)
{
	struct espconn *pespconn = arg;
	os_printf("=== PUMP CONNECTED ===\n");
	if (pespconn && pespconn->proto.tcp) {
		os_printf("Pump IP: %d.%d.%d.%d:%d\n",
			pespconn->proto.tcp->remote_ip[0], pespconn->proto.tcp->remote_ip[1],
			pespconn->proto.tcp->remote_ip[2], pespconn->proto.tcp->remote_ip[3],
			pespconn->proto.tcp->remote_port);
		os_printf("Local IP: %d.%d.%d.%d:%d\n",
			pespconn->proto.tcp->local_ip[0], pespconn->proto.tcp->local_ip[1],
			pespconn->proto.tcp->local_ip[2], pespconn->proto.tcp->local_ip[3],
			pespconn->proto.tcp->local_port);
	} else {
		os_printf("Pump connected (details unavailable)\n");
	}
	os_printf("Ready to receive Modbus commands from pump\n");
}

/******************************************************************************
 * FunctionName : user_tcp_disconnect_cb
 * Description  : tcp connection disconnected callback
 * Parameters   : arg -- Additional argument to pass to the callback function
 * Returns      : none
*******************************************************************************/
LOCAL void ICACHE_FLASH_ATTR
user_tcp_disconnect_cb(void *arg)
{
	struct espconn *pespconn = arg;
	os_printf("=== PUMP DISCONNECTED ===\n");
	if (pespconn && pespconn->proto.tcp) {
		os_printf("Pump IP: %d.%d.%d.%d:%d disconnected\n",
			pespconn->proto.tcp->remote_ip[0], pespconn->proto.tcp->remote_ip[1],
			pespconn->proto.tcp->remote_ip[2], pespconn->proto.tcp->remote_ip[3],
			pespconn->proto.tcp->remote_port);
	} else {
		os_printf("Pump disconnected (details unavailable)\n");
	}
	os_printf("Waiting for pump to reconnect...\n");
}

/******************************************************************************
 * FunctionName : user_tcp_reconnect_cb
 * Description  : tcp connection reconnect callback
 * Parameters   : arg -- Additional argument to pass to the callback function
 * Returns      : none
*******************************************************************************/
LOCAL void ICACHE_FLASH_ATTR
user_tcp_reconnect_cb(void *arg, sint8 err)
{
	struct espconn *pespconn = arg;
	// 服务器模式下，重连错误通常表示客户端连接问题
	os_printf("Modbus TCP client connection error: %d (client may have disconnected)\n", err);
}

 /******************************************************************************
      * FunctionName : user_tcp_sent_cb
      * Description  : tcp sent successfully
      * Parameters  : arg -- Additional argument to pass to the callback function
      * Returns      : none
 *******************************************************************************/
LOCAL void ICACHE_FLASH_ATTR
user_tcp_sent_cb(void *arg)
{
	struct espconn *pespconn = arg;
	tcpTx_buf_pionter = tcpTx_buf_pionter + TCP_LENGTH;

//	os_printf("-%d- ",tcpWait2SendBytes);
	if(tcpWait2SendBytes > TCP_LENGTH) {
		tst_tcpCnt0++;
		tcpWait2SendBytes = tcpWait2SendBytes - TCP_LENGTH;
		espconn_send(&PhoneConn, tcpTx_buf_pionter, TCP_LENGTH);
//		os_printf("x%d ",tst_tcpCnt0);
	}
	else if(tcpWait2SendBytes>0){
		tst_tcpCnt1++;
		espconn_send(&PhoneConn, tcpTx_buf_pionter, tcpWait2SendBytes);
		tcpWait2SendBytes = 0;
//		os_printf("u%d ",tst_tcpCnt1);
	}

	//GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0);
	//os_printf("tcp_send successfully !!!\n");
}
/******************************************************************************
 * FunctionName : user_devicefind_init
 * Description  : create a tcp connection
 * Parameters   : none
 * Returns      : none
*******************************************************************************/
void ICACHE_FLASH_ATTR
user_tcp_init(void)
{
	struct ip_info ipConfig;

	// 获取当前Station模式的IP地址
	wifi_get_ip_info(STATION_IF, &ipConfig);

	PhoneConn.type = ESPCONN_TCP;
	PhoneConn.proto.tcp = &PhoneConnTcp;
	PhoneConn.proto.tcp->local_port = 502;//Modbus TCP标准端口

	// 设置本地IP为当前Station IP
	PhoneConn.proto.tcp->local_ip[0] = (ipConfig.ip.addr) & 0xFF;
	PhoneConn.proto.tcp->local_ip[1] = (ipConfig.ip.addr >> 8) & 0xFF;
	PhoneConn.proto.tcp->local_ip[2] = (ipConfig.ip.addr >> 16) & 0xFF;
	PhoneConn.proto.tcp->local_ip[3] = (ipConfig.ip.addr >> 24) & 0xFF;

	os_printf("Modbus TCP Sensor Server listening on %d.%d.%d.%d:%d\n",
		PhoneConn.proto.tcp->local_ip[0], PhoneConn.proto.tcp->local_ip[1],
		PhoneConn.proto.tcp->local_ip[2], PhoneConn.proto.tcp->local_ip[3],
		PhoneConn.proto.tcp->local_port);
	os_printf("Pump (*************) can now connect to get sensor data\n");

	// 注册回调函数
	espconn_regist_recvcb(&PhoneConn, modbus_tcp_recv); // 注册Modbus TCP数据包接收回调
	espconn_regist_sentcb(&PhoneConn, user_tcp_sent_cb); // 注册TCP数据发送回调
	espconn_regist_connectcb(&PhoneConn, user_tcp_connect_cb); // 注册TCP连接建立回调
	espconn_regist_disconcb(&PhoneConn, user_tcp_disconnect_cb); // 注册TCP断开连接回调

	// 作为TCP服务器监听连接，而不是主动连接
	espconn_accept(&PhoneConn); // 监听TCP连接
	espconn_regist_time(&PhoneConn, 0, 0); // 设置超时时间为0（无超时）
}
void ICACHE_FLASH_ATTR
my_user_tcp_init(uint32 ipAddress)
{
	// 对于Modbus TCP服务器，不需要指定远程IP
	// 直接调用标准的TCP初始化函数
	os_printf("Reinitializing Modbus TCP server (ignoring remote IP: %d.%d.%d.%d)\n",
		(ipAddress>>24)&0x000000ff, (ipAddress>>16)&0x000000ff,
		(ipAddress>>8)&0x000000ff, (ipAddress)&0x000000ff);

	// 重新初始化TCP服务器
	user_tcp_init();
}


/******************************************************************************
 * FunctionName : user_set_station_config
 * Description  : set SSID and password for ESP8266 station mode
 * Parameters   : none
 * Returns      : none
*******************************************************************************/
void ICACHE_FLASH_ATTR
user_set_station_config(void)
{
	struct station_config stationConf;
	struct ip_info ipConfig;

	os_memset(&stationConf, 0, sizeof(struct station_config));

	// 设置泵的WiFi配置
	os_memcpy(ssid_buf, "USR-W610_A018", os_strlen("USR-W610_A018"));
	os_memcpy(password_buf, "", 0);
	os_memcpy(stationConf.ssid, ssid_buf, os_strlen("USR-W610_A018"));
	os_memcpy(stationConf.password, password_buf, 0);
	os_printf("Connecting to pump WiFi: %s\n", ssid_buf);

	wifi_station_set_config(&stationConf);

	// 设置固定IP地址 ***********
	wifi_station_dhcpc_stop(); // 停止DHCP客户端

	// 配置静态IP
	IP4_ADDR(&ipConfig.ip, 192, 168, 2, 5);        // 传感器IP: ***********
	IP4_ADDR(&ipConfig.netmask, 255, 255, 255, 0); // 子网掩码: *************
	IP4_ADDR(&ipConfig.gw, 192, 168, 2, 3);        // 网关: ***********

	wifi_set_ip_info(STATION_IF, &ipConfig);

	os_printf("Static IP configured: ***********, Gateway: ***********\n");
}
void ICACHE_FLASH_ATTR
my_set_softap_config()//my_set_softap_config(char *ssid, char *password)
{
	char ssid[15];
	char password[15];
	struct softap_config config;
 
	wifi_softap_get_config(&config); // Get config first.
    
	os_memset(config.ssid, 0, 32);
	os_memset(config.password, 0, 64);
	if(readFlash_SsidPassword() == 0) {
		os_memcpy(config.ssid, ssid_buf, os_strlen(ssid_buf));
		os_memcpy(config.password, password_buf, os_strlen(password_buf));
	}
	else {
		os_memcpy(config.ssid, "PREMAT3#null", 12);
		os_memcpy(config.password, "12345678", 8);
	}
	config.authmode = AUTH_WPA_WPA2_PSK;
	config.ssid_len = 0;// or its actual length
	config.max_connection = 4; // how many stations can connect to ESP8266 softAP at most.
 
	wifi_softap_set_config(&config);// Set ESP8266 softap config .
    
}
LOCAL void ICACHE_FLASH_ATTR
timer_callback(void *arg)
{
	// 喂看门狗防止重启
	system_soft_wdt_feed();

	// 只有在WiFi连接时才发送数据
	if(tcpDataNeedSend && wifi_connected) {
		tcpDataNeedSend = 0;
//		uart0_tx_buffer("T ", 2);
		tcpTx_buf_pionter = tcpTx_buf0;
		tst_tcpCnt0 = 0;
		tst_tcpCnt1 = 0;
		if(tcpWait2SendBytes > TCP_LENGTH) {
			tcpWait2SendBytes = tcpWait2SendBytes - TCP_LENGTH;
			espconn_send(&PhoneConn, tcpTx_buf_pionter, TCP_LENGTH);
		}
		else {
			tcpWait2SendBytes = 0;
			espconn_send(&PhoneConn, tcpTx_buf_pionter, tcpWait2SendBytes);
		}
		GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0); // HSPI 可以接收新数据
	}
}
/******************************************************************************
 * FunctionName : user_rf_cal_sector_set
 * Description  : SDK just reversed 4 sectors, used for rf init data and paramters.
 *                We add this function to force users to set rf cal sector, since
 *                we don't know which sector is free in user's application.
 *                sector map for last several sectors : ABCCC
 *                A : rf cal
 *                B : rf init data
 *                C : sdk parameters
 * Parameters   : none
 * Returns      : rf cal sector
*******************************************************************************/
uint32 ICACHE_FLASH_ATTR
user_rf_cal_sector_set(void)
{
    enum flash_size_map size_map = system_get_flash_size_map();
    uint32 rf_cal_sec = 0;

    switch (size_map) {
        case FLASH_SIZE_4M_MAP_256_256:
            rf_cal_sec = 128 - 5;
            break;

        case FLASH_SIZE_8M_MAP_512_512:
            rf_cal_sec = 256 - 5;
            break;

        case FLASH_SIZE_16M_MAP_512_512:
        case FLASH_SIZE_16M_MAP_1024_1024:
            rf_cal_sec = 512 - 5;
            break;

        case FLASH_SIZE_32M_MAP_512_512:
        case FLASH_SIZE_32M_MAP_1024_1024:
            rf_cal_sec = 1024 - 5;
            break;

        default:
            rf_cal_sec = 0;
            break;
    }

    return rf_cal_sec;
}

void ICACHE_FLASH_ATTR
user_rf_pre_init(void)
{
}


uint8 tcpTx_busy = 0;
uint8 tcpPackageId = 0;
uint8 tcpFrameId = 0;
uint8 tst_Framecnt = 0;
uint16 tcpBuf_index = 8;
uint16 tcpBuf_frameHead = 0;
uint16 tstCnt=0;
uint16 tcpSingleFrameFullCnt = 0;
 /******************************************************************************
      * FunctionName : user_tcp_send
      * Description  : tcp send data
      * Parameters  : none
      * Returns      : none
 *******************************************************************************/
 LOCAL void ICACHE_FLASH_ATTR
 user_tcp_send(uint16 length)
 {
 //    char DeviceBuffer[40] = {0};
	char hwaddr[6];
	struct ip_info ipconfig;

	// TCP不需要每次设置远程IP，连接建立后就固定了
	// const char tcp_remote_ip[4] = { 172, 20, 10, 1};  // TCP服务器地址
	// os_memcpy(PhoneConn.proto.tcp->remote_ip, tcp_remote_ip, 4);
	// PhoneConn.proto.tcp->remote_port = 8888;

	wifi_get_macaddr(STATION_IF, hwaddr);
	espconn_send(&PhoneConn, tcpTx_buf0, length);//发送数据
    // espconn_sent(&user_tcp_espconn, DeviceBuffer, os_strlen(DeviceBuffer));

 }
void  SPI_Receive_Data(void) {
	uint8 i = 0;
	uint32 recv_data0, recv_data;

	recv_data0 = READ_PERI_REG(SPI_W0(SpiNum_HSPI)); // 读取前4字节

	// 打印所有SPI接收到的数据，用于调试
	os_printf("=== SPI RAW RECEIVED ===\n");
	os_printf("recv_data0: 0x%08X, low4bits: 0x%X\n", recv_data0, recv_data0 & 0x0000000f);

	// 强制更新Modbus寄存器，不管数据格式如何
	{
		uint32_t spi_data[8];
		uint8 j;
		for(j = 0; j < 8; j++) {
			spi_data[j] = READ_PERI_REG(SPI_W0(SpiNum_HSPI)+(j<<2));
		}
		os_printf("=== FORCE UPDATE MODBUS ===\n");
		os_printf("SPI data: ");
		for(j = 0; j < 8; j++) {
			os_printf("0x%08X ", spi_data[j]);
			if(j % 4 == 3) os_printf("\n          ");
		}
		os_printf("\n");
		update_modbus_from_stm32_data(spi_data, 8);
	}

	if((recv_data0 & 0x0000000f) == 0x0D) { //数据帧
		os_printf("SPI data frame detected (0x0D)\n");
		if(tcpBuf_index>8296){
			if(debugModeOpen) {
				os_printf("Over4095\n\r",i,recv_data);
			}
		}
		tcpPackageId = (recv_data0 >> 8) & 0x000000ff;
		tcpTx_buf0[tcpBuf_index++] = (recv_data0>>16) & 0xff;
		tcpTx_buf0[tcpBuf_index++] = (recv_data0>>24) & 0xff;
		tstCnt = tstCnt + 2;
		for(i = 1; i < 8; i++) {
			recv_data = READ_PERI_REG(SPI_W0(SpiNum_HSPI)+(i<<2));
			tcpTx_buf0[tcpBuf_index++] = recv_data & 0xff;
			tcpTx_buf0[tcpBuf_index++] = (recv_data>>8) & 0xff;
			tcpTx_buf0[tcpBuf_index++] = (recv_data>>16) & 0xff;
			tcpTx_buf0[tcpBuf_index++] = (recv_data>>24) & 0xff;
			tstCnt = tstCnt + 4;
		}
		tcpSingleFrameFullCnt = tcpSingleFrameFullCnt + 30;

		// 更新Modbus输入寄存器 - 将SPI接收的数据映射到Modbus寄存器
		uint32_t spi_data[8];
		for(i = 0; i < 8; i++) {
			spi_data[i] = READ_PERI_REG(SPI_W0(SpiNum_HSPI)+(i<<2));
		}

		// 打印SPI接收到的原始数据
		os_printf("=== SPI DATA RECEIVED ===\n");
		os_printf("SPI raw data: ");
		for(i = 0; i < 8; i++) {
			os_printf("0x%08X ", spi_data[i]);
			if(i % 4 == 3) os_printf("\n              ");
		}
		os_printf("\n");

		// 更新Modbus寄存器
		update_modbus_from_stm32_data(spi_data, 8);
		if(recv_data0 & 0x000000f0) { // 尾帧判断
			tcpBuf_index = tcpBuf_index - (30-(((recv_data0>>4)&0x0000000f)<<1));//修正有效长度
			tcpTx_buf0[tcpBuf_frameHead] = 0x0D;
			tcpTx_buf0[tcpBuf_frameHead+1] = tcpPackageId; // 包ID
			tcpTx_buf0[tcpBuf_frameHead+2] = tcpFrameId; // 帧ID
			tcpTx_buf0[tcpBuf_frameHead+3] = 0x01; // 尾帧标识
			tcpTx_buf0[tcpBuf_frameHead+4] = ((tcpBuf_index-tcpBuf_frameHead)>>8)&0x00ff; // 有效数据长度
			tcpTx_buf0[tcpBuf_frameHead+5] = (tcpBuf_index-tcpBuf_frameHead)&0x00ff; // 有效数据长度 1400
			tst_Framecnt++;
//			os_printf("E%d,%d ",tcpBuf_index,(tcpBuf_index-tcpBuf_frameHead));
			if(debugModeOpen) {
				os_printf("slv_d");
			}
			tcpDataNeedSend = 1;
			tcpWait2SendBytes = tcpBuf_index;
			tcpBuf_index = 8;
			tcpBuf_frameHead = 0;
			tcpFrameId = 0;
			tcpSingleFrameFullCnt = 0;
			tstCnt = 0;
		}
		else if(tcpSingleFrameFullCnt == 1440) {
			tcpTx_buf0[tcpBuf_frameHead] = 0x0D;
			tcpTx_buf0[tcpBuf_frameHead+1] = tcpPackageId; // 包ID
			tcpTx_buf0[tcpBuf_frameHead+2] = tcpFrameId++; // 帧ID
			tcpTx_buf0[tcpBuf_frameHead+3] = 0x00; // 尾帧标识
			tcpTx_buf0[tcpBuf_frameHead+4] = 0x05; // 有效数据长度
			tcpTx_buf0[tcpBuf_frameHead+5] = 0xA0; // 有效数据长度 1440
			tcpBuf_index = tcpBuf_index + 8;
			tcpBuf_frameHead = tcpBuf_frameHead + TCP_LENGTH;
			tcpSingleFrameFullCnt = 0;
		}
//		os_printf("send to App Data");
		GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0); // HSPI 可以接收新数据
	}
	else if((recv_data0 & 0x000000ff) == 0x0A) { // 发送给 APP 的指令
		uint8 idex = 0;
		for(i = 0; i < 8; i++) {
			recv_data = READ_PERI_REG(SPI_W0(SpiNum_HSPI)+(i<<2));
			tcpTxCmd_buf[idex++] = recv_data & 0xff;
			tcpTxCmd_buf[idex++] = (recv_data>>8) & 0xff;
			tcpTxCmd_buf[idex++] = (recv_data>>16) & 0xff;
			tcpTxCmd_buf[idex++] = (recv_data>>24) & 0xff;
		}
		// 只有在WiFi连接时才发送数据
		if(wifi_connected) {
			espconn_send(&PhoneConn, tcpTxCmd_buf, idex);//发送数据
			if(debugModeOpen) {
				os_printf("send to App ");
			}
		} else {
			if(debugModeOpen) {
				os_printf("WiFi not connected, data not sent ");
			}
		}
		GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0); // HSPI 可以接收新数据
	}
	else if((recv_data0 & 0x000000ff) == 0x0E) { // 发送给 ESP8266 的指令
		uint8 index = 0;
		uint8 j = 0;
		tst_Framecnt = 0;
		tcpRx_buf[index++] = (recv_data0>>16) & 0xff;
		tcpRx_buf[index++] = (recv_data0>>24) & 0xff;
		for(i = 1; i < 8; i++) {
			recv_data = READ_PERI_REG(SPI_W0(SpiNum_HSPI)+(i<<2));
			tcpRx_buf[index++] = recv_data & 0xff;
			tcpRx_buf[index++] = (recv_data>>8) & 0xff;
			tcpRx_buf[index++] = (recv_data>>16) & 0xff;
			tcpRx_buf[index++] = (recv_data>>24) & 0xff;
		}
		if(((recv_data0>>8) & 0x000000ff) == 0x05) { // 修改 ssid 和 密码
			// 更新WiFi Station配置
			for( i = 0; i < 32; i++) {
				ssid_buf[i] = tcpRx_buf[j++];
			}
			for( i = 0; i < 32; i++) {
				 password_buf[i] = tcpRx_buf[j++];
			}
			saveData2Flash();

			// 重新配置Station模式并连接
			wifi_connected = 0;
			user_set_station_config();
			wifi_station_disconnect();
			wifi_station_connect();
			os_printf("WiFi config updated, reconnecting...\n");
		}
		else if(((recv_data0>>8) & 0x000000ff) == 0x06) { // 修改 TCP 远端 ip 地址
			uint8 index = 0;
			uint32 ipAddress;
			// tcpRx_buf[index++] = (recv_data0>>16) & 0xff;
			// tcpRx_buf[index++] = (recv_data0>>24) & 0xff;
			// for(i = 1; i < 8; i++) {
				// recv_data = READ_PERI_REG(SPI_W0(SpiNum_HSPI)+(i<<2));
				// tcpRx_buf[index++] = recv_data & 0xff;
				// tcpRx_buf[index++] = (recv_data>>8) & 0xff;
				// tcpRx_buf[index++] = (recv_data>>16) & 0xff;
				// tcpRx_buf[index++] = (recv_data>>24) & 0xff;
			// }
			ipAddress = (tcpRx_buf[0]<<24) | (tcpRx_buf[1]<<16) | (tcpRx_buf[2]<<8) | tcpRx_buf[3];
			if(debugModeOpen) {
				os_printf(" from STM32 ipAddress %d",ipAddress);
			}
//			os_printf(" form STM32 ipAddress %d",ipAddress);
			my_user_tcp_init(ipAddress);
		}
		else if(((recv_data0>>8) & 0x000000ff) == 0x02) { // 开启调试串口打印
			debugModeOpen = (tcpRx_buf[4] == 0x00) ? 0x00 : 0x01;
			os_printf("debugMode=%d, ESP8266 firmware version=%d", debugModeOpen, version);
		}

	}
	else {
		// 不符合任何已知格式的数据，但仍然尝试更新Modbus寄存器
		os_printf("SPI unknown format data, trying to update Modbus anyway\n");
		uint32_t spi_data[8];
		uint8 i;
		for(i = 0; i < 8; i++) {
			spi_data[i] = READ_PERI_REG(SPI_W0(SpiNum_HSPI)+(i<<2));
		}

		// 打印SPI接收到的原始数据
		os_printf("=== SPI UNKNOWN DATA ===\n");
		os_printf("SPI raw data: ");
		for(i = 0; i < 8; i++) {
			os_printf("0x%08X ", spi_data[i]);
			if(i % 4 == 3) os_printf("\n              ");
		}
		os_printf("\n");

		// 更新Modbus寄存器
		update_modbus_from_stm32_data(spi_data, 8);
	}
	//return 0;
}

// SPI interrupt callback function.
void spi_slave_isr_sta(void *para)
{
    uint32 regvalue;
    uint32 statusW, statusR, counter;
	uint32 recv_data[16]; 
	
    if (READ_PERI_REG(0x3ff00020)&BIT4) {
        //following 3 lines is to clear isr signal
        CLEAR_PERI_REG_MASK(SPI_SLAVE(SpiNum_SPI), 0x3ff);
    } else if (READ_PERI_REG(0x3ff00020)&BIT7) { //bit7 is for hspi isr,
		GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 1);
        regvalue = READ_PERI_REG(SPI_SLAVE(SpiNum_HSPI));
//        os_printf("spi_slave_isr_sta SPI_SLAVE[0x%08x]\n\r", regvalue);
        SPIIntClear(SpiNum_HSPI);
        SET_PERI_REG_MASK(SPI_SLAVE(SpiNum_HSPI), SPI_SYNC_RESET);
        SPIIntClear(SpiNum_HSPI);
        
        SPIIntEnable(SpiNum_HSPI, SpiIntSrc_WrStaDone
                 | SpiIntSrc_RdStaDone 
                 | SpiIntSrc_WrBufDone 
                 | SpiIntSrc_RdBufDone);

        if (regvalue & SPI_SLV_WR_BUF_DONE) {
			char i = 0;			
            // User can get data from the W0~W7
//           os_printf("spi_slave_isr_sta : SPI_SLV_WR_BUF_DONE\n\r");
			SPI_Receive_Data();
        }
		else if (regvalue & SPI_SLV_RD_BUF_DONE) {
            // TO DO 
//            os_printf("spi_slave_isr_sta : SPI_SLV_RD_BUF_DONE\n\r"); 
			if(tcpRxCacheCnt > 0) {
				tcpRxCacheCnt--;
			}
			else {
				tcpRxCacheCnt = 0;
			}
			if(debugModeOpen) {
				os_printf("slv_r(%d) ", tcpRxCacheCnt);
			}
			if(tcpRxCacheCnt > 0) {
				SPISlaveSendData(SpiNum_HSPI, tcpRxBuf0, 8);

				switch(tcpRxCacheCnt) {
					case 1:
						break;
					case 2: memcpy(tcpRxBuf0, tcpRxBuf1, 32);
						break;
					case 3: memcpy(tcpRxBuf0, tcpRxBuf1, 32);
						memcpy(tcpRxBuf1, tcpRxBuf2, 32);
						break;
					case 4: memcpy(tcpRxBuf0, tcpRxBuf1, 32);
						memcpy(tcpRxBuf1, tcpRxBuf2, 32);
						memcpy(tcpRxBuf2, tcpRxBuf3, 32);
						break;
					case 5: memcpy(tcpRxBuf0, tcpRxBuf1, 32);
						memcpy(tcpRxBuf1, tcpRxBuf2, 32);
						memcpy(tcpRxBuf2, tcpRxBuf3, 32);
						memcpy(tcpRxBuf3, tcpRxBuf4, 32);
						break;
					default:
						if(debugModeOpen) {
							os_printf("slv_r_over(%d) ", tcpRxCacheCnt);
						}
						break;
				}
				GPIO_OUTPUT_SET(GPIO_ID_PIN(2), 1);// GPIO2 输出高，数据读取完毕
			}
			else {
				GPIO_OUTPUT_SET(GPIO_ID_PIN(2), 0);// GPIO2 输出低，数据读取完毕
			}
			GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0); // HSPI 可以接收新数据
			
        }
        if (regvalue & SPI_SLV_RD_STA_DONE) {
            statusR = READ_PERI_REG(SPI_RD_STATUS(SpiNum_HSPI));
            statusW = READ_PERI_REG(SPI_WR_STATUS(SpiNum_HSPI));
			if(debugModeOpen) {
				os_printf("spi_slave_isr_sta : SPI_SLV_RD_STA_DONE[R=0x%08x,W=0x%08x]\n\r", statusR, statusW);
			}
			GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0); // HSPI 可以接收新数据
        }

        if (regvalue & SPI_SLV_WR_STA_DONE) {
            statusR = READ_PERI_REG(SPI_RD_STATUS(SpiNum_HSPI));
            statusW = READ_PERI_REG(SPI_WR_STATUS(SpiNum_HSPI));
			if(debugModeOpen) {
				os_printf("spi_slave_isr_sta : SPI_SLV_WR_STA_DONE[R=0x%08x,W=0x%08x]\n\r", statusR, statusW);
			}
			GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0); // HSPI 可以接收新数据
        }
        if ((regvalue & SPI_TRANS_DONE) && ((regvalue & 0xf) == 0)) {
			if(debugModeOpen) {
				os_printf("spi_slave_isr_sta : SPI_TRANS_DONE\n\r");
			}
			GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0); // HSPI 可以接收新数据
        }
        //SHOWSPIREG(SpiNum_HSPI);
    }
	
}

// Test spi slave interfaces.
void ICACHE_FLASH_ATTR spi_slave_init()
{
    //
    SpiAttr hSpiAttr;
    hSpiAttr.bitOrder = SpiBitOrder_MSBFirst;
    hSpiAttr.speed = 0;
    hSpiAttr.mode = SpiMode_Slave;
    hSpiAttr.subMode = SpiSubMode_0;

    // Init HSPI GPIO
    WRITE_PERI_REG(PERIPHS_IO_MUX, 0x105);
    PIN_FUNC_SELECT(PERIPHS_IO_MUX_MTDI_U, 2);//configure io to spi mode
    PIN_FUNC_SELECT(PERIPHS_IO_MUX_MTCK_U, 2);//configure io to spi mode
    PIN_FUNC_SELECT(PERIPHS_IO_MUX_MTMS_U, 2);//configure io to spi mode
    PIN_FUNC_SELECT(PERIPHS_IO_MUX_MTDO_U, 2);//configure io to spi mode

    os_printf("\r\n == spi init slave ==\r\n");
    SPIInit(SpiNum_HSPI, &hSpiAttr);
    
    // Set spi interrupt information.
    SpiIntInfo spiInt;
    spiInt.src = (SpiIntSrc_TransDone 
        | SpiIntSrc_WrStaDone 
        |SpiIntSrc_RdStaDone 
        |SpiIntSrc_WrBufDone 
        |SpiIntSrc_RdBufDone);
    spiInt.isrFunc = spi_slave_isr_sta;
    SPIIntCfg(SpiNum_HSPI, &spiInt);
   // SHOWSPIREG(SpiNum_HSPI);
    
    SPISlaveRecvData(SpiNum_HSPI);
    // uint32_t sndData[8] = { 0 };
    // sndData[0] = 0x35343332;
    // sndData[1] = 0x39383736;
    // sndData[2] = 0x3d3c3b3a;
    // sndData[3] = 0x11103f3e;
    // sndData[4] = 0x15141312;
    // sndData[5] = 0x19181716;
    // sndData[6] = 0x1d1c1b1a;
    // sndData[7] = 0x21201f1e;

//    SPISlaveSendData(SpiNum_HSPI, sndData, 8);
//    WRITE_PERI_REG(SPI_RD_STATUS(SpiNum_HSPI), 0x8A);
//    WRITE_PERI_REG(SPI_WR_STATUS(SpiNum_HSPI), 0x83);
}

/**
 * @brief Test spi interfaces.
 *
 */
void ICACHE_FLASH_ATTR
user_init(void)
{
	int i = 0;
	uart_init(BIT_RATE_115200,BIT_RATE_115200);
	os_printf("ESP8266 Station Mode Initialization\n");

	// 设置为Station模式
	wifi_set_opmode(STATION_MODE); // 0x01:Station、0x02:SoftAP、0x03:Station+SoftAP

	// 配置Station模式的WiFi连接参数
	user_set_station_config();

	// 初始化定时器 - 改为100ms间隔，减少系统负载
	os_timer_disarm(&test_timer);
	os_timer_setfn(&test_timer, (os_timer_func_t *)timer_callback, NULL);
	os_timer_arm(&test_timer, 100, 1);

	// 初始化WiFi连接检查定时器
	os_timer_disarm(&wifi_check_timer);
	os_timer_setfn(&wifi_check_timer, (os_timer_func_t *)wifi_check_ip, NULL);
	os_timer_arm(&wifi_check_timer, 1000, 0);

	// WiFi Station配置
	wifi_station_set_reconnect_policy(true);  // 启用自动重连
	wifi_station_set_auto_connect(1);         // 启用自动连接
	wifi_set_sleep_type(NONE_SLEEP_T);
	// 不要停止看门狗，而是定期喂狗
	// system_soft_wdt_stop();

	// 开始连接WiFi
	wifi_station_connect();
	os_printf("Connecting to WiFi...\n");

	// IO2 配置为普通输出IO，通知STM32读取数据，0无新数据,1有新数据
	PIN_FUNC_SELECT(PERIPHS_IO_MUX_GPIO2_U, FUNC_GPIO0);
	// IO4 配置为普通输出IO，通知STM32当前Busy, 0空闲, 1忙
	PIN_FUNC_SELECT(PERIPHS_IO_MUX_GPIO4_U, FUNC_GPIO0);
	// IO2 IO4 初始化
	GPIO_OUTPUT_SET(GPIO_ID_PIN(2), 0); // IO2 置低
	GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0); // IO4 置低

	// 初始化SPI从机接口
	spi_slave_init();

	// 初始化Modbus TCP
	modbus_tcp_init();

	// STM32数据处理已恢复
	{
		os_printf("STM32 data processing ENABLED\n");
		os_printf("Waiting for real STM32 data via SPI\n");

		// 注释掉测试数据，使用STM32真实数据
		// os_printf("\n=== EXECUTING SPECIFIC RESPONSE TEST ===\n");
		// test_specific_response();

		os_printf("ESP8266 will use STM32[1] data for Modbus registers\n");
	}

	os_printf("ESP8266 Station Mode with Modbus TCP Init Complete\n");
}

