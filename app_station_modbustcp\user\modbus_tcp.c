#include "ets_sys.h"
#include "os_type.h"
#include "osapi.h"
#include "mem.h"
#include "user_interface.h"
#include "ip_addr.h"

#include "espconn.h"
#include "modbus_tcp.h"

// 外部函数声明
extern void ICACHE_FLASH_ATTR send_modbus_data_to_stm32(uint16 start_addr, uint16 quantity);

// 全局Modbus数据存储
static modbus_data_t modbus_data;

/******************************************************************************
 * FunctionName : modbus_data_init
 * Description  : 初始化Modbus数据存储
 * Parameters   : none
 * Returns      : none
*******************************************************************************/
void ICACHE_FLASH_ATTR
modbus_data_init(void)
{
    uint16 i;
    
    // 初始化保持寄存器
    for (i = 0; i < 1000; i++) {
        modbus_data.holding_registers[i] = 0;
    }
    
    // 初始化输入寄存器
    for (i = 0; i < 1000; i++) {
        modbus_data.input_registers[i] = 0;
    }
    
    // 初始化线圈
    for (i = 0; i < 1000; i++) {
        modbus_data.coils[i] = 0;
    }
    
    // 初始化离散输入
    for (i = 0; i < 1000; i++) {
        modbus_data.discrete_inputs[i] = 0;
    }
    
    os_printf("Modbus data initialized\n");
}

/******************************************************************************
 * FunctionName : modbus_bytes_to_uint16
 * Description  : 将两个字节转换为uint16 (大端序)
 * Parameters   : bytes -- 字节数组指针
 * Returns      : uint16值
*******************************************************************************/
uint16 ICACHE_FLASH_ATTR
modbus_bytes_to_uint16(uint8 *bytes)
{
    return (uint16)((bytes[0] << 8) | bytes[1]);
}

/******************************************************************************
 * FunctionName : modbus_uint16_to_bytes
 * Description  : 将uint16转换为两个字节 (大端序)
 * Parameters   : value -- uint16值
 *                bytes -- 输出字节数组指针
 * Returns      : none
*******************************************************************************/
void ICACHE_FLASH_ATTR
modbus_uint16_to_bytes(uint16 value, uint8 *bytes)
{
    bytes[0] = (uint8)(value >> 8);
    bytes[1] = (uint8)(value & 0xFF);
}

/******************************************************************************
 * FunctionName : modbus_get_holding_register
 * Description  : 获取保持寄存器值
 * Parameters   : addr -- 寄存器地址 (0-based)
 * Returns      : 寄存器值
*******************************************************************************/
uint16 ICACHE_FLASH_ATTR
modbus_get_holding_register(uint16 addr)
{
    if (addr < 1000) {
        return modbus_data.holding_registers[addr];
    }
    return 0;
}

/******************************************************************************
 * FunctionName : modbus_set_holding_register
 * Description  : 设置保持寄存器值
 * Parameters   : addr -- 寄存器地址 (0-based)
 *                value -- 寄存器值
 * Returns      : none
*******************************************************************************/
void ICACHE_FLASH_ATTR
modbus_set_holding_register(uint16 addr, uint16 value)
{
    if (addr < 1000) {
        modbus_data.holding_registers[addr] = value;
    }
}

/******************************************************************************
 * FunctionName : modbus_get_input_register
 * Description  : 获取输入寄存器值
 * Parameters   : addr -- 寄存器地址 (0-based)
 * Returns      : 寄存器值
*******************************************************************************/
uint16 ICACHE_FLASH_ATTR
modbus_get_input_register(uint16 addr)
{
    if (addr < 1000) {
        return modbus_data.input_registers[addr];
    }
    return 0;
}

/******************************************************************************
 * FunctionName : modbus_set_input_register
 * Description  : 设置输入寄存器值
 * Parameters   : addr -- 寄存器地址 (0-based)
 *                value -- 寄存器值
 * Returns      : none
*******************************************************************************/
void ICACHE_FLASH_ATTR
modbus_set_input_register(uint16 addr, uint16 value)
{
    if (addr < 1000) {
        modbus_data.input_registers[addr] = value;
    }
}

/******************************************************************************
 * FunctionName : modbus_get_coil
 * Description  : 获取线圈状态
 * Parameters   : addr -- 线圈地址 (0-based)
 * Returns      : 线圈状态 (0或1)
*******************************************************************************/
uint8 ICACHE_FLASH_ATTR
modbus_get_coil(uint16 addr)
{
    if (addr < 1000) {
        return modbus_data.coils[addr];
    }
    return 0;
}

/******************************************************************************
 * FunctionName : modbus_set_coil
 * Description  : 设置线圈状态
 * Parameters   : addr -- 线圈地址 (0-based)
 *                value -- 线圈状态 (0或1)
 * Returns      : none
*******************************************************************************/
void ICACHE_FLASH_ATTR
modbus_set_coil(uint16 addr, uint8 value)
{
    if (addr < 1000) {
        modbus_data.coils[addr] = (value != 0) ? 1 : 0;
    }
}

/******************************************************************************
 * FunctionName : modbus_get_discrete_input
 * Description  : 获取离散输入状态
 * Parameters   : addr -- 输入地址 (0-based)
 * Returns      : 输入状态 (0或1)
*******************************************************************************/
uint8 ICACHE_FLASH_ATTR
modbus_get_discrete_input(uint16 addr)
{
    if (addr < 1000) {
        return modbus_data.discrete_inputs[addr];
    }
    return 0;
}

/******************************************************************************
 * FunctionName : modbus_set_discrete_input
 * Description  : 设置离散输入状态
 * Parameters   : addr -- 输入地址 (0-based)
 *                value -- 输入状态 (0或1)
 * Returns      : none
*******************************************************************************/
void ICACHE_FLASH_ATTR
modbus_set_discrete_input(uint16 addr, uint8 value)
{
    if (addr < 1000) {
        modbus_data.discrete_inputs[addr] = (value != 0) ? 1 : 0;
    }
}

/******************************************************************************
 * FunctionName : modbus_read_coils
 * Description  : 读取线圈状态 (功能码 0x01)
 * Parameters   : start_addr -- 起始地址
 *                quantity -- 数量
 *                response_data -- 响应数据缓冲区
 * Returns      : 响应数据长度
*******************************************************************************/
uint16 ICACHE_FLASH_ATTR
modbus_read_coils(uint16 start_addr, uint16 quantity, uint8 *response_data)
{
    uint16 i, byte_count;
    uint8 bit_index, byte_value;

    if (quantity == 0 || quantity > 2000 || start_addr + quantity > 1000) {
        return 0; // 非法参数
    }

    byte_count = (quantity + 7) / 8; // 计算需要的字节数
    response_data[0] = byte_count;

    // 清零响应数据
    for (i = 1; i <= byte_count; i++) {
        response_data[i] = 0;
    }

    // 填充线圈状态
    for (i = 0; i < quantity; i++) {
        if (modbus_get_coil(start_addr + i)) {
            bit_index = i % 8;
            byte_value = 1 << bit_index;
            response_data[1 + (i / 8)] |= byte_value;
        }
    }

    return byte_count + 1; // 字节计数 + 数据字节
}

/******************************************************************************
 * FunctionName : modbus_read_discrete_inputs
 * Description  : 读取离散输入状态 (功能码 0x02)
 * Parameters   : start_addr -- 起始地址
 *                quantity -- 数量
 *                response_data -- 响应数据缓冲区
 * Returns      : 响应数据长度
*******************************************************************************/
uint16 ICACHE_FLASH_ATTR
modbus_read_discrete_inputs(uint16 start_addr, uint16 quantity, uint8 *response_data)
{
    uint16 i, byte_count;
    uint8 bit_index, byte_value;

    if (quantity == 0 || quantity > 2000 || start_addr + quantity > 1000) {
        return 0; // 非法参数
    }

    byte_count = (quantity + 7) / 8; // 计算需要的字节数
    response_data[0] = byte_count;

    // 清零响应数据
    for (i = 1; i <= byte_count; i++) {
        response_data[i] = 0;
    }

    // 填充离散输入状态
    for (i = 0; i < quantity; i++) {
        if (modbus_get_discrete_input(start_addr + i)) {
            bit_index = i % 8;
            byte_value = 1 << bit_index;
            response_data[1 + (i / 8)] |= byte_value;
        }
    }

    return byte_count + 1; // 字节计数 + 数据字节
}

/******************************************************************************
 * FunctionName : modbus_read_holding_registers
 * Description  : 读取保持寄存器 (功能码 0x03)
 * Parameters   : start_addr -- 起始地址
 *                quantity -- 数量
 *                response_data -- 响应数据缓冲区
 * Returns      : 响应数据长度
*******************************************************************************/
uint16 ICACHE_FLASH_ATTR
modbus_read_holding_registers(uint16 start_addr, uint16 quantity, uint8 *response_data)
{
    uint16 i, reg_value;

    if (quantity == 0 || quantity > 125 || start_addr + quantity > 1000) {
        return 0; // 非法参数
    }

    response_data[0] = quantity * 2; // 字节计数

    // 填充寄存器值
    for (i = 0; i < quantity; i++) {
        reg_value = modbus_get_holding_register(start_addr + i);
        modbus_uint16_to_bytes(reg_value, &response_data[1 + i * 2]);
    }

    return quantity * 2 + 1; // 字节计数 + 数据字节
}

/******************************************************************************
 * FunctionName : modbus_read_input_registers
 * Description  : 读取输入寄存器 (功能码 0x04)
 * Parameters   : start_addr -- 起始地址
 *                quantity -- 数量
 *                response_data -- 响应数据缓冲区
 * Returns      : 响应数据长度
*******************************************************************************/
uint16 ICACHE_FLASH_ATTR
modbus_read_input_registers(uint16 start_addr, uint16 quantity, uint8 *response_data)
{
    uint16 i, reg_value;

    if (quantity == 0 || quantity > 125 || start_addr + quantity > 1000) {
        return 0; // 非法参数
    }

    response_data[0] = quantity * 2; // 字节计数

    // 填充寄存器值
    for (i = 0; i < quantity; i++) {
        reg_value = modbus_get_input_register(start_addr + i);
        modbus_uint16_to_bytes(reg_value, &response_data[1 + i * 2]);
    }

    return quantity * 2 + 1; // 字节计数 + 数据字节
}

/******************************************************************************
 * FunctionName : modbus_write_single_coil
 * Description  : 写单个线圈 (功能码 0x05)
 * Parameters   : addr -- 线圈地址
 *                value -- 线圈值 (0x0000 或 0xFF00)
 * Returns      : 成功返回0，失败返回异常码
*******************************************************************************/
uint8 ICACHE_FLASH_ATTR
modbus_write_single_coil(uint16 addr, uint16 value)
{
    if (addr >= 1000) {
        return MODBUS_EXCEPTION_ILLEGAL_DATA_ADDRESS;
    }

    if (value != 0x0000 && value != 0xFF00) {
        return MODBUS_EXCEPTION_ILLEGAL_DATA_VALUE;
    }

    modbus_set_coil(addr, (value == 0xFF00) ? 1 : 0);
    return 0; // 成功
}

/******************************************************************************
 * FunctionName : modbus_write_single_register
 * Description  : 写单个寄存器 (功能码 0x06)
 * Parameters   : addr -- 寄存器地址
 *                value -- 寄存器值
 * Returns      : 成功返回0，失败返回异常码
*******************************************************************************/
uint8 ICACHE_FLASH_ATTR
modbus_write_single_register(uint16 addr, uint16 value)
{
    if (addr >= 1000) {
        return MODBUS_EXCEPTION_ILLEGAL_DATA_ADDRESS;
    }

    // 保护STM32数据区域（地址0-15），不允许外部写入
    if (addr < 16) {
        return MODBUS_EXCEPTION_ILLEGAL_DATA_ADDRESS;
    }

    modbus_set_holding_register(addr, value);

    // 发送更新的数据到STM32（从地址16开始）
    send_modbus_data_to_stm32(addr - 16, 1);

    return 0; // 成功
}

/******************************************************************************
 * FunctionName : modbus_write_multiple_coils
 * Description  : 写多个线圈 (功能码 0x0F)
 * Parameters   : start_addr -- 起始地址
 *                quantity -- 数量
 *                data -- 数据
 * Returns      : 成功返回0，失败返回异常码
*******************************************************************************/
uint8 ICACHE_FLASH_ATTR
modbus_write_multiple_coils(uint16 start_addr, uint16 quantity, uint8 *data)
{
    uint16 i;
    uint8 byte_index, bit_index;

    if (quantity == 0 || quantity > 1968 || start_addr + quantity > 1000) {
        return MODBUS_EXCEPTION_ILLEGAL_DATA_ADDRESS;
    }

    // 写入线圈值
    for (i = 0; i < quantity; i++) {
        byte_index = i / 8;
        bit_index = i % 8;
        modbus_set_coil(start_addr + i, (data[byte_index] >> bit_index) & 0x01);
    }

    return 0; // 成功
}

/******************************************************************************
 * FunctionName : modbus_write_multiple_registers
 * Description  : 写多个寄存器 (功能码 0x10)
 * Parameters   : start_addr -- 起始地址
 *                quantity -- 数量
 *                data -- 数据
 * Returns      : 成功返回0，失败返回异常码
*******************************************************************************/
uint8 ICACHE_FLASH_ATTR
modbus_write_multiple_registers(uint16 start_addr, uint16 quantity, uint8 *data)
{
    uint16 i, reg_value;

    if (quantity == 0 || quantity > 123 || start_addr + quantity > 1000) {
        return MODBUS_EXCEPTION_ILLEGAL_DATA_ADDRESS;
    }

    // 保护STM32数据区域（地址0-15），不允许外部写入
    if (start_addr < 16) {
        return MODBUS_EXCEPTION_ILLEGAL_DATA_ADDRESS;
    }

    // 写入寄存器值
    for (i = 0; i < quantity; i++) {
        reg_value = modbus_bytes_to_uint16(&data[i * 2]);
        modbus_set_holding_register(start_addr + i, reg_value);
    }

    // 发送更新的数据到STM32（从地址16开始，所以减去16）
    send_modbus_data_to_stm32(start_addr - 16, quantity);

    return 0; // 成功
}

/******************************************************************************
 * FunctionName : modbus_tcp_send_exception
 * Description  : 发送Modbus异常响应
 * Parameters   : conn -- TCP连接
 *                transaction_id -- 事务ID
 *                unit_id -- 单元ID
 *                function_code -- 功能码
 *                exception_code -- 异常码
 * Returns      : none
*******************************************************************************/
void ICACHE_FLASH_ATTR
modbus_tcp_send_exception(struct espconn *conn, uint16 transaction_id, uint8 unit_id, uint8 function_code, uint8 exception_code)
{
    uint8 response[9];

    // MBAP Header
    modbus_uint16_to_bytes(transaction_id, &response[0]);
    modbus_uint16_to_bytes(0, &response[2]); // Protocol ID
    modbus_uint16_to_bytes(3, &response[4]); // Length (Unit ID + Function Code + Exception Code)
    response[6] = unit_id;

    // PDU
    response[7] = function_code | 0x80; // 异常响应功能码
    response[8] = exception_code;

    espconn_send(conn, response, 9);
    os_printf("Modbus exception sent: FC=0x%02X, EC=0x%02X\n", function_code, exception_code);
}

/******************************************************************************
 * FunctionName : modbus_tcp_send_response
 * Description  : 发送Modbus TCP响应
 * Parameters   : conn -- TCP连接
 *                response -- 响应ADU
 * Returns      : none
*******************************************************************************/
void ICACHE_FLASH_ATTR
modbus_tcp_send_response(struct espconn *conn, modbus_tcp_adu_t *response)
{
    uint8 buffer[MODBUS_TCP_ADU_MAX_LENGTH];
    uint16 total_length;

    // 构建MBAP Header
    modbus_uint16_to_bytes(response->mbap.transaction_id, &buffer[0]);
    modbus_uint16_to_bytes(response->mbap.protocol_id, &buffer[2]);
    modbus_uint16_to_bytes(response->mbap.length, &buffer[4]);
    buffer[6] = response->mbap.unit_id;

    // 复制PDU
    os_memcpy(&buffer[7], response->pdu, response->pdu_length);

    total_length = MODBUS_MBAP_HEADER_LENGTH + 1 + response->pdu_length;

    // 打印ESP8266发送给泵的具体数值
    os_printf("=== ESP8266 RESPONSE ===\n");
    os_printf("Function Code: 0x%02X\n", response->pdu[0]);
    os_printf("Total Length: %d bytes\n", total_length);
    os_printf("Data Length: %d bytes\n", response->pdu_length - 1);

    // 打印响应中的数据值
    if (response->pdu_length > 1 && response->pdu[0] == MODBUS_FC_READ_HOLDING_REGISTERS) {
        uint8 byte_count = response->pdu[1];
        uint16 i;
        os_printf("Register Values: ");
        for (i = 0; i < byte_count/2 && i < 20; i++) {
            uint16 reg_value = (response->pdu[2 + i*2] << 8) | response->pdu[3 + i*2];
            os_printf("0x%04X ", reg_value);
            if (i % 8 == 7) os_printf("\n                 ");
        }
        os_printf("\n");
    }

    // 打印原始响应数据
    {
        uint16 i;
        os_printf("Raw Response: ");
        for (i = 0; i < total_length && i < 40; i++) {
            os_printf("%02X ", buffer[i]);
            if (i % 16 == 15) os_printf("\n              ");
        }
        os_printf("\n");
    }

    espconn_send(conn, buffer, total_length);
}

/******************************************************************************
 * FunctionName : modbus_tcp_process_request
 * Description  : 处理Modbus TCP请求
 * Parameters   : arg -- TCP连接参数
 *                data -- 接收到的数据
 *                len -- 数据长度
 * Returns      : none
*******************************************************************************/
void ICACHE_FLASH_ATTR
modbus_tcp_process_request(void *arg, char *data, unsigned short len)
{
    struct espconn *conn = (struct espconn *)arg;
    modbus_tcp_adu_t request, response;
    uint16 start_addr, quantity, reg_value;
    uint8 exception_code, byte_count;
    uint16 response_len;

    // 检查最小长度
    if (len < MODBUS_MBAP_HEADER_LENGTH + 2) {
        os_printf("Modbus request too short: %d bytes\n", len);
        return;
    }

    // 解析MBAP Header
    request.mbap.transaction_id = modbus_bytes_to_uint16((uint8*)&data[0]);
    request.mbap.protocol_id = modbus_bytes_to_uint16((uint8*)&data[2]);
    request.mbap.length = modbus_bytes_to_uint16((uint8*)&data[4]);
    request.mbap.unit_id = data[6];

    // 检查协议ID
    if (request.mbap.protocol_id != 0) {
        os_printf("Invalid protocol ID: %d\n", request.mbap.protocol_id);
        return;
    }

    // 复制PDU
    request.pdu_length = len - MODBUS_MBAP_HEADER_LENGTH - 1;
    os_memcpy(request.pdu, &data[7], request.pdu_length);

    // 打印泵发送的具体命令详情
    os_printf("=== PUMP COMMAND ===\n");
    os_printf("Transaction ID: %d\n", request.mbap.transaction_id);
    os_printf("Function Code: 0x%02X\n", request.pdu[0]);
    os_printf("Request Length: %d bytes\n", len);

    if (request.pdu_length >= 5) {
        uint16 start_addr = modbus_bytes_to_uint16(&request.pdu[1]);
        uint16 quantity = modbus_bytes_to_uint16(&request.pdu[3]);
        os_printf("Start Address: %d\n", start_addr);
        os_printf("Quantity: %d registers\n", quantity);
        os_printf("Expected Data: %d bytes\n", quantity * 2);
    }

    // 打印原始请求数据
    {
        uint16 i;
        os_printf("Raw Request: ");
        for (i = 0; i < len && i < 20; i++) {
            os_printf("%02X ", (uint8)data[i]);
        }
        os_printf("\n");
    }

    // 准备响应MBAP Header
    response.mbap.transaction_id = request.mbap.transaction_id;
    response.mbap.protocol_id = 0;
    response.mbap.unit_id = request.mbap.unit_id;

    // 处理不同的功能码
    switch (request.pdu[0]) {
        case MODBUS_FC_READ_COILS:
            if (request.pdu_length < 5) {
                modbus_tcp_send_exception(conn, request.mbap.transaction_id,
                                        request.mbap.unit_id, request.pdu[0],
                                        MODBUS_EXCEPTION_ILLEGAL_DATA_VALUE);
                return;
            }
            start_addr = modbus_bytes_to_uint16(&request.pdu[1]);
            quantity = modbus_bytes_to_uint16(&request.pdu[3]);

            response.pdu[0] = MODBUS_FC_READ_COILS;
            response_len = modbus_read_coils(start_addr, quantity, &response.pdu[1]);
            if (response_len == 0) {
                modbus_tcp_send_exception(conn, request.mbap.transaction_id,
                                        request.mbap.unit_id, request.pdu[0],
                                        MODBUS_EXCEPTION_ILLEGAL_DATA_ADDRESS);
                return;
            }
            response.pdu_length = response_len + 1;
            break;

        case MODBUS_FC_READ_DISCRETE_INPUTS:
            if (request.pdu_length < 5) {
                modbus_tcp_send_exception(conn, request.mbap.transaction_id,
                                        request.mbap.unit_id, request.pdu[0],
                                        MODBUS_EXCEPTION_ILLEGAL_DATA_VALUE);
                return;
            }
            start_addr = modbus_bytes_to_uint16(&request.pdu[1]);
            quantity = modbus_bytes_to_uint16(&request.pdu[3]);

            response.pdu[0] = MODBUS_FC_READ_DISCRETE_INPUTS;
            response_len = modbus_read_discrete_inputs(start_addr, quantity, &response.pdu[1]);
            if (response_len == 0) {
                modbus_tcp_send_exception(conn, request.mbap.transaction_id,
                                        request.mbap.unit_id, request.pdu[0],
                                        MODBUS_EXCEPTION_ILLEGAL_DATA_ADDRESS);
                return;
            }
            response.pdu_length = response_len + 1;
            break;

        case MODBUS_FC_READ_HOLDING_REGISTERS:
            if (request.pdu_length < 5) {
                modbus_tcp_send_exception(conn, request.mbap.transaction_id,
                                        request.mbap.unit_id, request.pdu[0],
                                        MODBUS_EXCEPTION_ILLEGAL_DATA_VALUE);
                return;
            }
            start_addr = modbus_bytes_to_uint16(&request.pdu[1]);
            quantity = modbus_bytes_to_uint16(&request.pdu[3]);

            response.pdu[0] = MODBUS_FC_READ_HOLDING_REGISTERS;
            response_len = modbus_read_holding_registers(start_addr, quantity, &response.pdu[1]);
            if (response_len == 0) {
                modbus_tcp_send_exception(conn, request.mbap.transaction_id,
                                        request.mbap.unit_id, request.pdu[0],
                                        MODBUS_EXCEPTION_ILLEGAL_DATA_ADDRESS);
                return;
            }
            response.pdu_length = response_len + 1;
            break;

        case MODBUS_FC_READ_INPUT_REGISTERS:
            if (request.pdu_length < 5) {
                modbus_tcp_send_exception(conn, request.mbap.transaction_id,
                                        request.mbap.unit_id, request.pdu[0],
                                        MODBUS_EXCEPTION_ILLEGAL_DATA_VALUE);
                return;
            }
            start_addr = modbus_bytes_to_uint16(&request.pdu[1]);
            quantity = modbus_bytes_to_uint16(&request.pdu[3]);

            response.pdu[0] = MODBUS_FC_READ_INPUT_REGISTERS;
            response_len = modbus_read_input_registers(start_addr, quantity, &response.pdu[1]);
            if (response_len == 0) {
                modbus_tcp_send_exception(conn, request.mbap.transaction_id,
                                        request.mbap.unit_id, request.pdu[0],
                                        MODBUS_EXCEPTION_ILLEGAL_DATA_ADDRESS);
                return;
            }
            response.pdu_length = response_len + 1;
            break;

        case MODBUS_FC_WRITE_SINGLE_COIL:
            if (request.pdu_length < 5) {
                modbus_tcp_send_exception(conn, request.mbap.transaction_id,
                                        request.mbap.unit_id, request.pdu[0],
                                        MODBUS_EXCEPTION_ILLEGAL_DATA_VALUE);
                return;
            }
            start_addr = modbus_bytes_to_uint16(&request.pdu[1]);
            reg_value = modbus_bytes_to_uint16(&request.pdu[3]);

            exception_code = modbus_write_single_coil(start_addr, reg_value);
            if (exception_code != 0) {
                modbus_tcp_send_exception(conn, request.mbap.transaction_id,
                                        request.mbap.unit_id, request.pdu[0],
                                        exception_code);
                return;
            }

            // 回显请求
            response.pdu[0] = MODBUS_FC_WRITE_SINGLE_COIL;
            modbus_uint16_to_bytes(start_addr, &response.pdu[1]);
            modbus_uint16_to_bytes(reg_value, &response.pdu[3]);
            response.pdu_length = 5;
            break;

        case MODBUS_FC_WRITE_SINGLE_REGISTER:
            if (request.pdu_length < 5) {
                modbus_tcp_send_exception(conn, request.mbap.transaction_id,
                                        request.mbap.unit_id, request.pdu[0],
                                        MODBUS_EXCEPTION_ILLEGAL_DATA_VALUE);
                return;
            }
            start_addr = modbus_bytes_to_uint16(&request.pdu[1]);
            reg_value = modbus_bytes_to_uint16(&request.pdu[3]);

            exception_code = modbus_write_single_register(start_addr, reg_value);
            if (exception_code != 0) {
                modbus_tcp_send_exception(conn, request.mbap.transaction_id,
                                        request.mbap.unit_id, request.pdu[0],
                                        exception_code);
                return;
            }

            // 回显请求
            response.pdu[0] = MODBUS_FC_WRITE_SINGLE_REGISTER;
            modbus_uint16_to_bytes(start_addr, &response.pdu[1]);
            modbus_uint16_to_bytes(reg_value, &response.pdu[3]);
            response.pdu_length = 5;
            break;

        case MODBUS_FC_WRITE_MULTIPLE_COILS:
            if (request.pdu_length < 6) {
                modbus_tcp_send_exception(conn, request.mbap.transaction_id,
                                        request.mbap.unit_id, request.pdu[0],
                                        MODBUS_EXCEPTION_ILLEGAL_DATA_VALUE);
                return;
            }
            start_addr = modbus_bytes_to_uint16(&request.pdu[1]);
            quantity = modbus_bytes_to_uint16(&request.pdu[3]);
            byte_count = request.pdu[5];

            if (request.pdu_length < 6 + byte_count) {
                modbus_tcp_send_exception(conn, request.mbap.transaction_id,
                                        request.mbap.unit_id, request.pdu[0],
                                        MODBUS_EXCEPTION_ILLEGAL_DATA_VALUE);
                return;
            }

            exception_code = modbus_write_multiple_coils(start_addr, quantity, &request.pdu[6]);
            if (exception_code != 0) {
                modbus_tcp_send_exception(conn, request.mbap.transaction_id,
                                        request.mbap.unit_id, request.pdu[0],
                                        exception_code);
                return;
            }

            // 响应
            response.pdu[0] = MODBUS_FC_WRITE_MULTIPLE_COILS;
            modbus_uint16_to_bytes(start_addr, &response.pdu[1]);
            modbus_uint16_to_bytes(quantity, &response.pdu[3]);
            response.pdu_length = 5;
            break;

        case MODBUS_FC_WRITE_MULTIPLE_REGISTERS:
            if (request.pdu_length < 6) {
                modbus_tcp_send_exception(conn, request.mbap.transaction_id,
                                        request.mbap.unit_id, request.pdu[0],
                                        MODBUS_EXCEPTION_ILLEGAL_DATA_VALUE);
                return;
            }
            start_addr = modbus_bytes_to_uint16(&request.pdu[1]);
            quantity = modbus_bytes_to_uint16(&request.pdu[3]);
            byte_count = request.pdu[5];

            if (request.pdu_length < 6 + byte_count || byte_count != quantity * 2) {
                modbus_tcp_send_exception(conn, request.mbap.transaction_id,
                                        request.mbap.unit_id, request.pdu[0],
                                        MODBUS_EXCEPTION_ILLEGAL_DATA_VALUE);
                return;
            }

            exception_code = modbus_write_multiple_registers(start_addr, quantity, &request.pdu[6]);
            if (exception_code != 0) {
                modbus_tcp_send_exception(conn, request.mbap.transaction_id,
                                        request.mbap.unit_id, request.pdu[0],
                                        exception_code);
                return;
            }

            // 响应
            response.pdu[0] = MODBUS_FC_WRITE_MULTIPLE_REGISTERS;
            modbus_uint16_to_bytes(start_addr, &response.pdu[1]);
            modbus_uint16_to_bytes(quantity, &response.pdu[3]);
            response.pdu_length = 5;
            break;

        default:
            // 不支持的功能码
            modbus_tcp_send_exception(conn, request.mbap.transaction_id,
                                    request.mbap.unit_id, request.pdu[0],
                                    MODBUS_EXCEPTION_ILLEGAL_FUNCTION);
            return;
    }

    // 设置响应长度
    response.mbap.length = response.pdu_length + 1; // PDU长度 + 单元ID

    // 发送响应
    modbus_tcp_send_response(conn, &response);
}

/******************************************************************************
 * FunctionName : modbus_tcp_init
 * Description  : 初始化Modbus TCP
 * Parameters   : none
 * Returns      : none
*******************************************************************************/
void ICACHE_FLASH_ATTR
modbus_tcp_init(void)
{
    // 初始化Modbus数据
    modbus_data_init();

    os_printf("Modbus TCP initialized\n");
}
