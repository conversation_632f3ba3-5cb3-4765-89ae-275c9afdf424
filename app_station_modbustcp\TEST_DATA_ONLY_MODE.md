# 测试数据专用模式

## 当前配置

ESP8266现在配置为**测试数据专用模式**：
- ❌ **STM32数据处理已禁用**
- ✅ **只发送固定的测试数据**
- ✅ **每5秒刷新一次测试数据**

## 禁用的功能

### 1. SPI数据处理
```c
// 原来的强制更新已禁用
// update_modbus_from_stm32_data(spi_data, 8);

// 现在只输出：
"SPI data processing DISABLED - using test data only"
```

### 2. STM32数据映射
```c
// 原来的自定义映射已禁用
// 不再处理STM32[1]的数据

// 现在只使用固定测试数据
```

### 3. 数据监控
```c
// 原来的数据监控定时器已禁用
// data_monitor_timer_cb() 不再执行

// 现在使用测试数据刷新定时器
```

## 启用的功能

### 1. 固定测试响应
ESP8266将始终发送：
```
Register[0] = 0x0226 (550)
Register[1] = 0x0000 (0)
Raw Response: 01 03 04 02 26 00 00
```

### 2. 定期刷新
每5秒执行一次`test_specific_response()`，确保测试数据始终有效。

### 3. Modbus TCP服务
正常的Modbus TCP服务继续运行，泵可以正常连接和读取数据。

## 预期输出

### 初始化时
```
STM32 data processing DISABLED
Setting up test response data only

=== EXECUTING SPECIFIC RESPONSE TEST ===
=== TEST SPECIFIC RESPONSE ===
Setting registers to produce response: 01 03 04 02 26 00 00
Set Register[0] = 0x0226 (decimal: 550)
Set Register[1] = 0x0000 (decimal: 0)
✓ Test data set successfully!
✓ Pump should receive: 01 03 04 02 26 00 00

Sensor ready! Pump can connect to 192.168.2.5:502 for Modbus TCP
STM32 data processing DISABLED - using test data only
Test data will be refreshed every 5 seconds
```

### 定期刷新（每5秒）
```
=== PERIODIC TEST DATA UPDATE #1 ===
=== TEST SPECIFIC RESPONSE ===
Setting registers to produce response: 01 03 04 02 26 00 00
Set Register[0] = 0x0226 (decimal: 550)
Set Register[1] = 0x0000 (decimal: 0)
✓ Test data set successfully!
✓ Pump should receive: 01 03 04 02 26 00 00
Test data refreshed. Next update in 5 seconds...
```

### SPI数据接收时
```
=== SPI RAW RECEIVED ===
recv_data0: 0x0000300A, low4bits: 0xA
SPI data processing DISABLED - using test data only
```

### 泵请求时
```
=== ESP8266 RESPONSE ===
Function Code: 0x03
Total Length: 13 bytes
Data Length: 5 bytes
Register Values: 0x0226 0x0000
Raw Response: 00 XX 00 00 00 07 01 03 04 02 26 00 00
```

## 数据一致性

### 固定响应
无论何时泵发送请求，都会收到相同的响应：
- **寄存器0**: 0x0226 (550)
- **寄存器1**: 0x0000 (0)
- **Raw Response**: `01 03 04 02 26 00 00`

### 数据刷新
每5秒重新设置一次相同的测试数据，确保：
1. 数据不会被意外覆盖
2. 寄存器值始终正确
3. 响应格式保持一致

## 优势

### 1. 可预测性
- 泵总是收到相同的数据
- 便于测试和调试
- 排除STM32数据变化的干扰

### 2. 稳定性
- 不依赖STM32的数据传输
- 不受SPI通信问题影响
- 数据格式完全可控

### 3. 测试友好
- 便于验证Modbus TCP通信
- 便于测试泵的数据处理逻辑
- 便于调试网络连接问题

## 恢复STM32数据处理

如果需要恢复STM32数据处理，需要：

1. **重新启用SPI数据处理**
2. **恢复自定义数据映射**
3. **切换回数据监控定时器**
4. **重新启用STM32数据更新**

## 当前状态总结

✅ **ESP8266 Modbus TCP服务正常运行**
✅ **固定测试数据 (0x0226, 0x0000) 持续发送**
✅ **每5秒刷新测试数据**
❌ **STM32数据处理已完全禁用**
❌ **SPI数据不再影响Modbus寄存器**

泵现在将始终收到固定的测试响应：`01 03 04 02 26 00 00`
