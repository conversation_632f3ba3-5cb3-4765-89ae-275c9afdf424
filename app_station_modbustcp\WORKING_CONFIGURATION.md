# ESP8266 Modbus TCP 工作配置

## 当前工作状态

ESP8266 Modbus TCP功能已经正常工作，能够正确响应泵的请求。

### 泵的请求配置
- **功能码**: 0x03 (读保持寄存器)
- **起始地址**: 0
- **寄存器数量**: 2
- **预期数据**: 4字节

### ESP8266的响应
- **功能码**: 0x03
- **数据长度**: 5字节 (1字节计数 + 4字节数据)
- **寄存器值**: 例如 0x1002, 0x0002

## 数据映射

STM32通过SPI发送的32位数据会被映射到Modbus寄存器：

```
STM32数据[0] (32位) → 寄存器0(高16位) + 寄存器1(低16位)
STM32数据[1] (32位) → 寄存器2(高16位) + 寄存器3(低16位)
...以此类推
```

**泵只读取寄存器0和寄存器1**，对应STM32数据[0]的值。

## 数据流程

1. **STM32 → ESP8266**: STM32通过SPI发送8个32位数据
2. **数据映射**: ESP8266将32位数据拆分为16个16位寄存器
3. **泵请求**: 泵通过Modbus TCP请求寄存器0和寄存器1
4. **ESP8266响应**: 返回寄存器0和寄存器1的值

## 重要注意事项

### 1. 泵只读取前4字节数据
泵只请求2个寄存器，因此只能获取STM32发送的第一个32位数据。如果需要传输更多数据，需要修改泵的配置。

### 2. 数据更新频率
STM32数据通过SPI更新到ESP8266，更新频率取决于STM32的发送频率。确保STM32定期发送最新数据。

### 3. 数据格式
STM32发送的第一个32位数据最重要，因为泵只读取这部分数据。确保将最关键的信息放在这个位置。

### 4. 调试信息
启用调试模式可以查看详细的数据流：
```
=== PUMP COMMAND ===
Transaction ID: 94
Function Code: 0x03
Start Address: 0
Quantity: 2 registers

=== ESP8266 RESPONSE ===
Register Values: 0x1002 0x0002
```

## 优化建议

### 1. 数据压缩
由于泵只读取4字节数据，可以考虑在STM32端压缩数据，将多个传感器值编码到一个32位数据中。

### 2. 数据优先级
将最重要的数据放在STM32数据[0]中，确保泵能读取到最关键的信息。

### 3. 数据格式约定
与泵端约定数据格式，例如：
- 高16位: 温度值 (0.01°C分辨率)
- 低16位: 压力值 (0.01kPa分辨率)

## 故障排除

### 如果泵无法读取数据
1. 检查ESP8266是否正确连接到泵的WiFi
2. 确认ESP8266的IP地址是***********
3. 验证STM32是否通过SPI发送数据
4. 检查寄存器0和寄存器1的值是否正确更新

### 如果数据不正确
1. 检查STM32发送的数据格式
2. 确认数据映射逻辑正确
3. 验证大小端序转换是否正确

## 结论

当前配置下，ESP8266能够正确响应泵的Modbus TCP请求，但只能传输4字节数据。如果需要传输更多数据，需要修改泵的配置，请求更多寄存器。
