/*
 * ESPRSSIF MIT License
 *
 * Copyright (c) 2015 <ESPRESSIF SYSTEMS (SHANGHAI) PTE LTD>
 *
 * Permission is hereby granted for use on ESPRESSIF SYSTEMS ESP8266 only, in which case,
 * it is free of charge, to any person obtaining a copy of this software and associated
 * documentation files (the "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the Software is furnished
 * to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all copies or
 * substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
 * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WH<PERSON>HER
 * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 *
 */
#include "ets_sys.h"
#include "os_type.h"
#include "osapi.h"
#include "spi_test.h"
#include "at_custom.h"
#include "user_interface.h"
#include "driver/uart.h"
#include "driver/spi_interface.h"
#include "espconn.h"
#include "mem.h"
#include "user_json.h"
#include "user_devicefind.h"
#include "eagle_soc.h"
#include "spi_flash.h"

LOCAL os_timer_t test_timer;
LOCAL os_timer_t wifi_check_timer;
struct espconn PhoneConn;
esp_tcp PhoneConnTcp;
LOCAL struct espconn user_tcp_espconn;
LOCAL struct espconn ptrespconn;
const char *ESP8266_MSG = "I'm ESP8266 ";
const char *device_find_request = "Are You ESP8266 Device?";

const char *device_find_response_ok = "Yes,I'm ESP8266!";

uint8 tcpTx_buf0[8292];
uint8 *tcpTx_buf_pionter;
char tcpRx_buf[32];
uint8 tcpTxCmd_buf[32];
uint8 tcpDataNeedSend = 0;
uint16 tcpWait2SendBytes = 0;
uint8 tst_tcpCnt0 = 0;
uint8 tst_tcpCnt1 = 0;
#define TCP_LENGTH 1448 // TCP 一帧数据长度1448字节

#define SEC 124           //读写的扇区（Sector）号
#define SEC_OFFSET 0//扇区内偏移量（必须是4的倍数）
#define FLASH_LEN 16//以读写16*4字节的数据为例（64字节用于存储SSID和密码）
uint8 flash_write_data[FLASH_LEN*4];
uint8 flash_read_data[FLASH_LEN*4];
uint8 ssid_buf[32] = "USR-W610_A018";     // 修改为要连接的WiFi SSID
uint8 password_buf[32] = ""; // 修改为要连接的WiFi密码
uint8 debugModeOpen = 0;
uint32 version = 20190117;
uint8 wifi_connected = 0;  // WiFi连接状态标志

// 函数声明
void ICACHE_FLASH_ATTR user_tcp_init(void);
void ICACHE_FLASH_ATTR user_set_station_config(void);
LOCAL void ICACHE_FLASH_ATTR wifi_check_ip(void *arg);

void saveData2Flash() {
	uint8 i = 0;
	uint8 j = 0;
//	os_printf("flash start");
	for(i = 0; i < 32; i++) {
		flash_write_data[j++] = ssid_buf[i];
	}
	for(i = 0; i < 32; i++) {
		flash_write_data[j++] = password_buf[i];
	}

	//写入数据
	spi_flash_erase_sector(SEC);
	spi_flash_write(SEC*4*1024+SEC_OFFSET, (uint32 *)&flash_write_data, FLASH_LEN*4);
	spi_flash_read(SEC*4*1024+SEC_OFFSET, (uint32 *)&flash_read_data, FLASH_LEN*4);
	for(i=0;i<64;i++){
	os_printf("flash[%d]%c ", i,flash_read_data[i]);
	}
}
uint8 readFlash_SsidPassword() {
	uint8 i = 0;
	uint8 j = 0;
	spi_flash_read(SEC*4*1024+SEC_OFFSET, (uint32 *)&flash_read_data, FLASH_LEN*4);
	for(i = 0; i < 32; i++) {
		ssid_buf[i] = flash_read_data[j++];
//		os_printf("s%d",ssid_buf[i]);
	}
	for(i = 0; i < 32; i++) {
		password_buf[i] = flash_read_data[j++];
//		os_printf("p%d",password_buf[i]);
	}
	if((ssid_buf[0] != 'q') || (ssid_buf[1] != 'w')) {
//		os_printf("return 1 ssid_buf[0]%d ",ssid_buf[0]);
		return 0x01;
	}
//	os_printf("return 0 ssid_buf[0]%d ",ssid_buf[0]);
	return 0;
}

/******************************************************************************
 * FunctionName : wifi_check_ip
 * Description  : check WiFi connection status and handle connection events
 * Parameters   : arg -- Additional argument to pass to the callback function
 * Returns      : none
*******************************************************************************/
LOCAL void ICACHE_FLASH_ATTR
wifi_check_ip(void *arg)
{
	struct ip_info ipConfig;
	uint8 wifiStatus;

	// 喂看门狗防止重启
	system_soft_wdt_feed();

	os_timer_disarm(&wifi_check_timer);
	wifi_get_ip_info(STATION_IF, &ipConfig);
	wifiStatus = wifi_station_get_connect_status();

	if (wifiStatus == STATION_GOT_IP && ipConfig.ip.addr != 0)
	{
		if (!wifi_connected) {
			wifi_connected = 1;
			os_printf("WiFi connected! IP: %d.%d.%d.%d\n",
				(ipConfig.ip.addr) & 0xFF,
				(ipConfig.ip.addr >> 8) & 0xFF,
				(ipConfig.ip.addr >> 16) & 0xFF,
				(ipConfig.ip.addr >> 24) & 0xFF);

			// WiFi连接完成，初始化TCP连接
			user_tcp_init();

			// 开始自动发送STM端数据
			os_printf("WiFi connected, ready to send STM data via TCP\n");
		}

		// 每2秒检查一次连接状态
		os_timer_setfn(&wifi_check_timer, (os_timer_func_t *)wifi_check_ip, NULL);
		os_timer_arm(&wifi_check_timer, 2000, 0);
	}
	else
	{
		wifi_connected = 0;

		if(wifi_station_get_connect_status() == STATION_WRONG_PASSWORD)
		{
			os_printf("STATION_WRONG_PASSWORD\n");
			wifi_station_connect();
		}
		else if(wifi_station_get_connect_status() == STATION_NO_AP_FOUND)
		{
			os_printf("STATION_NO_AP_FOUND\n");
			wifi_station_connect();
		}
		else if(wifi_station_get_connect_status() == STATION_CONNECT_FAIL)
		{
			os_printf("STATION_CONNECT_FAIL\n");
			wifi_station_connect();
		}
		else
		{
			os_printf("STATION_IDLE\n");
		}

		// 每500ms重试连接
		os_timer_setfn(&wifi_check_timer, (os_timer_func_t *)wifi_check_ip, NULL);
		os_timer_arm(&wifi_check_timer, 500, 0);
	}
}

uint32_t tcpRxBuf0[8] = {0};
uint32_t tcpRxBuf1[8] = {0};
uint32_t tcpRxBuf2[8] = {0};
uint32_t tcpRxBuf3[8] = {0};
uint32_t tcpRxBuf4[8] = {0};
uint8 tcpRxCacheCnt = 0; //接收到来自APP的数据包计数

void ICACHE_FLASH_ATTR
tcpclient_recv(void *arg, char *tcpRx_buf, unsigned short len)
{
	uint32_t sndData[8] = {0};
	uint8 i = 0;

	for(i = 0; i < 8; i++) {
		sndData[i] = (((uint32_t)tcpRx_buf[(i<<2)+3]) << 24) + (((uint32_t)tcpRx_buf[(i<<2)+2]) << 16) + (((uint32_t)tcpRx_buf[(i<<2)+1]) << 8) + (tcpRx_buf[(i<<2)]);
	}
	if(sndData[0] == 0x020EEFCD) { //APP开启ESP8266 debugMode
		debugModeOpen = (tcpRx_buf[4] == 0x00) ? 0x00 : 0x01;
		os_printf("debugMode=%d", debugModeOpen);
	}
	else {
		tcpRxCacheCnt++;
		switch(tcpRxCacheCnt) {
			case 1: SPISlaveSendData(SpiNum_HSPI, sndData, 8);
				break;
			case 2: memcpy(tcpRxBuf0, sndData, 32);
				break;
			case 3: memcpy(tcpRxBuf1, sndData, 32);
				break;
			case 4: memcpy(tcpRxBuf2, sndData, 32);
				break;
			case 5: memcpy(tcpRxBuf3, sndData, 32);
				break;
			case 6: memcpy(tcpRxBuf4, sndData, 32);
				break;
			default:
				break;
		}
		if(debugModeOpen) {
			os_printf("tcpRx(%d) ", tcpRxCacheCnt);
		}
		// WRITE_PERI_REG(SPI_RD_STATUS(SpiNum_HSPI), 0x8A);
		// WRITE_PERI_REG(SPI_WR_STATUS(SpiNum_HSPI), 0x83);
		GPIO_OUTPUT_SET(GPIO_ID_PIN(2), 1);//GPIO2输出高电平,通知STM32读取数据
	}
}

/******************************************************************************
 * FunctionName : user_tcp_connect_cb
 * Description  : tcp connection established callback
 * Parameters   : arg -- Additional argument to pass to the callback function
 * Returns      : none
*******************************************************************************/
LOCAL void ICACHE_FLASH_ATTR
user_tcp_connect_cb(void *arg)
{
	struct espconn *pespconn = arg;
	os_printf("TCP connection established\n");
}

/******************************************************************************
 * FunctionName : user_tcp_disconnect_cb
 * Description  : tcp connection disconnected callback
 * Parameters   : arg -- Additional argument to pass to the callback function
 * Returns      : none
*******************************************************************************/
LOCAL void ICACHE_FLASH_ATTR
user_tcp_disconnect_cb(void *arg)
{
	struct espconn *pespconn = arg;
	os_printf("TCP connection disconnected\n");
}

/******************************************************************************
 * FunctionName : user_tcp_reconnect_cb
 * Description  : tcp connection reconnect callback
 * Parameters   : arg -- Additional argument to pass to the callback function
 * Returns      : none
*******************************************************************************/
LOCAL void ICACHE_FLASH_ATTR
user_tcp_reconnect_cb(void *arg, sint8 err)
{
	struct espconn *pespconn = arg;
	os_printf("TCP connection reconnect, error: %d\n", err);
}

 /******************************************************************************
      * FunctionName : user_tcp_sent_cb
      * Description  : tcp sent successfully
      * Parameters  : arg -- Additional argument to pass to the callback function
      * Returns      : none
 *******************************************************************************/
LOCAL void ICACHE_FLASH_ATTR
user_tcp_sent_cb(void *arg)
{
	struct espconn *pespconn = arg;
	tcpTx_buf_pionter = tcpTx_buf_pionter + TCP_LENGTH;

//	os_printf("-%d- ",tcpWait2SendBytes);
	if(tcpWait2SendBytes > TCP_LENGTH) {
		tst_tcpCnt0++;
		tcpWait2SendBytes = tcpWait2SendBytes - TCP_LENGTH;
		espconn_send(&PhoneConn, tcpTx_buf_pionter, TCP_LENGTH);
//		os_printf("x%d ",tst_tcpCnt0);
	}
	else if(tcpWait2SendBytes>0){
		tst_tcpCnt1++;
		espconn_send(&PhoneConn, tcpTx_buf_pionter, tcpWait2SendBytes);
		tcpWait2SendBytes = 0;
//		os_printf("u%d ",tst_tcpCnt1);
	}

	//GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0);
	//os_printf("tcp_send successfully !!!\n");
}
/******************************************************************************
 * FunctionName : user_devicefind_init
 * Description  : create a tcp connection
 * Parameters   : none
 * Returns      : none
*******************************************************************************/
void ICACHE_FLASH_ATTR
user_tcp_init(void)
{
	struct ip_info ipConfig;

	// 获取当前Station模式的IP地址
	wifi_get_ip_info(STATION_IF, &ipConfig);

	PhoneConn.type = ESPCONN_TCP;
	PhoneConn.proto.tcp = &PhoneConnTcp;
	PhoneConn.proto.tcp->local_port = 502;//本地端口
	PhoneConn.proto.tcp->remote_port = 502;//远程端口

	// 设置本地IP为当前Station IP
	PhoneConn.proto.tcp->local_ip[0] = (ipConfig.ip.addr) & 0xFF;
	PhoneConn.proto.tcp->local_ip[1] = (ipConfig.ip.addr >> 8) & 0xFF;
	PhoneConn.proto.tcp->local_ip[2] = (ipConfig.ip.addr >> 16) & 0xFF;
	PhoneConn.proto.tcp->local_ip[3] = (ipConfig.ip.addr >> 24) & 0xFF;

	// 设置远程IP为服务器地址，基于172.20.10.x/28网络
	// TCP需要连接到具体的服务器IP，这里设置为***********
	PhoneConn.proto.tcp->remote_ip[0] = 192;
	PhoneConn.proto.tcp->remote_ip[1] = 168;
	PhoneConn.proto.tcp->remote_ip[2] = 2;
	PhoneConn.proto.tcp->remote_ip[3] = 211;

	os_printf("TCP Local IP: %d.%d.%d.%d:%d\n",
		PhoneConn.proto.tcp->local_ip[0], PhoneConn.proto.tcp->local_ip[1],
		PhoneConn.proto.tcp->local_ip[2], PhoneConn.proto.tcp->local_ip[3],
		PhoneConn.proto.tcp->local_port);
	os_printf("TCP Remote IP: %d.%d.%d.%d:%d\n",
		PhoneConn.proto.tcp->remote_ip[0], PhoneConn.proto.tcp->remote_ip[1],
		PhoneConn.proto.tcp->remote_ip[2], PhoneConn.proto.tcp->remote_ip[3],
		PhoneConn.proto.tcp->remote_port);

	espconn_regist_recvcb(&PhoneConn, tcpclient_recv); // 注册一个TCP数据包接收回调
	espconn_regist_sentcb(&PhoneConn, user_tcp_sent_cb); // register a tcp packet sent callback
	espconn_regist_connectcb(&PhoneConn, user_tcp_connect_cb); // 注册TCP连接建立回调
	espconn_regist_disconcb(&PhoneConn, user_tcp_disconnect_cb); // 注册TCP断开连接回调
	espconn_regist_reconcb(&PhoneConn, user_tcp_reconnect_cb); // 注册TCP重连回调
	espconn_connect(&PhoneConn);//建立 TCP 连接
}
void ICACHE_FLASH_ATTR
my_user_tcp_init(uint32 ipAddress)
{
	struct ip_info ipConfig;

	// 获取当前Station模式的IP地址
	wifi_get_ip_info(STATION_IF, &ipConfig);

	PhoneConn.type = ESPCONN_TCP;
	PhoneConn.proto.tcp = &PhoneConnTcp;
	PhoneConn.proto.tcp->local_port = 502;//本地端口
	PhoneConn.proto.tcp->remote_port = 502;//远程端口

	// 设置本地IP为当前Station IP
	PhoneConn.proto.tcp->local_ip[0] = (ipConfig.ip.addr) & 0xFF;
	PhoneConn.proto.tcp->local_ip[1] = (ipConfig.ip.addr >> 8) & 0xFF;
	PhoneConn.proto.tcp->local_ip[2] = (ipConfig.ip.addr >> 16) & 0xFF;
	PhoneConn.proto.tcp->local_ip[3] = (ipConfig.ip.addr >> 24) & 0xFF;

	PhoneConn.proto.tcp->remote_ip[0] = (ipAddress>>24)&0x000000ff;
	PhoneConn.proto.tcp->remote_ip[1] = (ipAddress>>16)&0x000000ff;
	PhoneConn.proto.tcp->remote_ip[2] = (ipAddress>>8)&0x000000ff;
	PhoneConn.proto.tcp->remote_ip[3] = (ipAddress)&0x000000ff;

	os_printf("%d.%d.%d.%d ",PhoneConn.proto.tcp->remote_ip[0],PhoneConn.proto.tcp->remote_ip[1],PhoneConn.proto.tcp->remote_ip[2],PhoneConn.proto.tcp->remote_ip[3]);
	espconn_regist_recvcb(&PhoneConn, tcpclient_recv); // 注册一个TCP数据包接收回调
	espconn_regist_sentcb(&PhoneConn, user_tcp_sent_cb); // register a tcp packet sent callback
	espconn_regist_connectcb(&PhoneConn, user_tcp_connect_cb); // 注册TCP连接建立回调
	espconn_regist_disconcb(&PhoneConn, user_tcp_disconnect_cb); // 注册TCP断开连接回调
	espconn_regist_reconcb(&PhoneConn, user_tcp_reconnect_cb); // 注册TCP重连回调
	espconn_connect(&PhoneConn);//建立 TCP 连接
}


/******************************************************************************
 * FunctionName : user_set_station_config
 * Description  : set SSID and password for ESP8266 station mode
 * Parameters   : none
 * Returns      : none
*******************************************************************************/
void ICACHE_FLASH_ATTR
user_set_station_config(void)
{
	struct station_config stationConf;

	os_memset(&stationConf, 0, sizeof(struct station_config));

	// 强制使用代码中设置的WiFi配置
	os_memcpy(ssid_buf, "USR-W610_A018", os_strlen("USR-W610_A018"));
	os_memcpy(password_buf, "", 0);
	os_memcpy(stationConf.ssid, ssid_buf, os_strlen("USR-W610_A018"));
	os_memcpy(stationConf.password, password_buf, 0);
	os_printf("Using WiFi config: %s\n", ssid_buf);

	wifi_station_set_config(&stationConf);
}
void ICACHE_FLASH_ATTR
my_set_softap_config()//my_set_softap_config(char *ssid, char *password)
{
	char ssid[15];
	char password[15];
	struct softap_config config;
 
	wifi_softap_get_config(&config); // Get config first.
    
	os_memset(config.ssid, 0, 32);
	os_memset(config.password, 0, 64);
	if(readFlash_SsidPassword() == 0) {
		os_memcpy(config.ssid, ssid_buf, os_strlen(ssid_buf));
		os_memcpy(config.password, password_buf, os_strlen(password_buf));
	}
	else {
		os_memcpy(config.ssid, "PREMAT3#null", 12);
		os_memcpy(config.password, "12345678", 8);
	}
	config.authmode = AUTH_WPA_WPA2_PSK;
	config.ssid_len = 0;// or its actual length
	config.max_connection = 4; // how many stations can connect to ESP8266 softAP at most.
 
	wifi_softap_set_config(&config);// Set ESP8266 softap config .
    
}
LOCAL void ICACHE_FLASH_ATTR
timer_callback(void *arg)
{
	// 喂看门狗防止重启
	system_soft_wdt_feed();

	// 只有在WiFi连接时才发送数据
	if(tcpDataNeedSend && wifi_connected) {
		tcpDataNeedSend = 0;
//		uart0_tx_buffer("T ", 2);
		tcpTx_buf_pionter = tcpTx_buf0;
		tst_tcpCnt0 = 0;
		tst_tcpCnt1 = 0;
		if(tcpWait2SendBytes > TCP_LENGTH) {
			tcpWait2SendBytes = tcpWait2SendBytes - TCP_LENGTH;
			espconn_send(&PhoneConn, tcpTx_buf_pionter, TCP_LENGTH);
		}
		else {
			tcpWait2SendBytes = 0;
			espconn_send(&PhoneConn, tcpTx_buf_pionter, tcpWait2SendBytes);
		}
		GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0); // HSPI 可以接收新数据
	}
}
/******************************************************************************
 * FunctionName : user_rf_cal_sector_set
 * Description  : SDK just reversed 4 sectors, used for rf init data and paramters.
 *                We add this function to force users to set rf cal sector, since
 *                we don't know which sector is free in user's application.
 *                sector map for last several sectors : ABCCC
 *                A : rf cal
 *                B : rf init data
 *                C : sdk parameters
 * Parameters   : none
 * Returns      : rf cal sector
*******************************************************************************/
uint32 ICACHE_FLASH_ATTR
user_rf_cal_sector_set(void)
{
    enum flash_size_map size_map = system_get_flash_size_map();
    uint32 rf_cal_sec = 0;

    switch (size_map) {
        case FLASH_SIZE_4M_MAP_256_256:
            rf_cal_sec = 128 - 5;
            break;

        case FLASH_SIZE_8M_MAP_512_512:
            rf_cal_sec = 256 - 5;
            break;

        case FLASH_SIZE_16M_MAP_512_512:
        case FLASH_SIZE_16M_MAP_1024_1024:
            rf_cal_sec = 512 - 5;
            break;

        case FLASH_SIZE_32M_MAP_512_512:
        case FLASH_SIZE_32M_MAP_1024_1024:
            rf_cal_sec = 1024 - 5;
            break;

        default:
            rf_cal_sec = 0;
            break;
    }

    return rf_cal_sec;
}

void ICACHE_FLASH_ATTR
user_rf_pre_init(void)
{
}


uint8 tcpTx_busy = 0;
uint8 tcpPackageId = 0;
uint8 tcpFrameId = 0;
uint8 tst_Framecnt = 0;
uint16 tcpBuf_index = 8;
uint16 tcpBuf_frameHead = 0;
uint16 tstCnt=0;
uint16 tcpSingleFrameFullCnt = 0;
 /******************************************************************************
      * FunctionName : user_tcp_send
      * Description  : tcp send data
      * Parameters  : none
      * Returns      : none
 *******************************************************************************/
 LOCAL void ICACHE_FLASH_ATTR
 user_tcp_send(uint16 length)
 {
 //    char DeviceBuffer[40] = {0};
	char hwaddr[6];
	struct ip_info ipconfig;

	// TCP不需要每次设置远程IP，连接建立后就固定了
	// const char tcp_remote_ip[4] = { 172, 20, 10, 1};  // TCP服务器地址
	// os_memcpy(PhoneConn.proto.tcp->remote_ip, tcp_remote_ip, 4);
	// PhoneConn.proto.tcp->remote_port = 8888;

	wifi_get_macaddr(STATION_IF, hwaddr);
	espconn_send(&PhoneConn, tcpTx_buf0, length);//发送数据
    // espconn_sent(&user_tcp_espconn, DeviceBuffer, os_strlen(DeviceBuffer));

 }
void  SPI_Receive_Data(void) {
	uint8 i = 0;
	uint32 recv_data0, recv_data;

	recv_data0 = READ_PERI_REG(SPI_W0(SpiNum_HSPI)); // 读取前4字节
	if((recv_data0 & 0x0000000f) == 0x0D) { //数据帧
		if(tcpBuf_index>8296){
			if(debugModeOpen) {
				os_printf("Over4095\n\r",i,recv_data);
			}
		}
		tcpPackageId = (recv_data0 >> 8) & 0x000000ff;
		tcpTx_buf0[tcpBuf_index++] = (recv_data0>>16) & 0xff;
		tcpTx_buf0[tcpBuf_index++] = (recv_data0>>24) & 0xff;
		tstCnt = tstCnt + 2;
		for(i = 1; i < 8; i++) {
			recv_data = READ_PERI_REG(SPI_W0(SpiNum_HSPI)+(i<<2));
			tcpTx_buf0[tcpBuf_index++] = recv_data & 0xff;
			tcpTx_buf0[tcpBuf_index++] = (recv_data>>8) & 0xff;
			tcpTx_buf0[tcpBuf_index++] = (recv_data>>16) & 0xff;
			tcpTx_buf0[tcpBuf_index++] = (recv_data>>24) & 0xff;
			tstCnt = tstCnt + 4;
		}
		tcpSingleFrameFullCnt = tcpSingleFrameFullCnt + 30;
		if(recv_data0 & 0x000000f0) { // 尾帧判断
			tcpBuf_index = tcpBuf_index - (30-(((recv_data0>>4)&0x0000000f)<<1));//修正有效长度
			tcpTx_buf0[tcpBuf_frameHead] = 0x0D;
			tcpTx_buf0[tcpBuf_frameHead+1] = tcpPackageId; // 包ID
			tcpTx_buf0[tcpBuf_frameHead+2] = tcpFrameId; // 帧ID
			tcpTx_buf0[tcpBuf_frameHead+3] = 0x01; // 尾帧标识
			tcpTx_buf0[tcpBuf_frameHead+4] = ((tcpBuf_index-tcpBuf_frameHead)>>8)&0x00ff; // 有效数据长度
			tcpTx_buf0[tcpBuf_frameHead+5] = (tcpBuf_index-tcpBuf_frameHead)&0x00ff; // 有效数据长度 1400
			tst_Framecnt++;
//			os_printf("E%d,%d ",tcpBuf_index,(tcpBuf_index-tcpBuf_frameHead));
			if(debugModeOpen) {
				os_printf("slv_d");
			}
			tcpDataNeedSend = 1;
			tcpWait2SendBytes = tcpBuf_index;
			tcpBuf_index = 8;
			tcpBuf_frameHead = 0;
			tcpFrameId = 0;
			tcpSingleFrameFullCnt = 0;
			tstCnt = 0;
		}
		else if(tcpSingleFrameFullCnt == 1440) {
			tcpTx_buf0[tcpBuf_frameHead] = 0x0D;
			tcpTx_buf0[tcpBuf_frameHead+1] = tcpPackageId; // 包ID
			tcpTx_buf0[tcpBuf_frameHead+2] = tcpFrameId++; // 帧ID
			tcpTx_buf0[tcpBuf_frameHead+3] = 0x00; // 尾帧标识
			tcpTx_buf0[tcpBuf_frameHead+4] = 0x05; // 有效数据长度
			tcpTx_buf0[tcpBuf_frameHead+5] = 0xA0; // 有效数据长度 1440
			tcpBuf_index = tcpBuf_index + 8;
			tcpBuf_frameHead = tcpBuf_frameHead + TCP_LENGTH;
			tcpSingleFrameFullCnt = 0;
		}
//		os_printf("send to App Data");
		GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0); // HSPI 可以接收新数据
	}
	else if((recv_data0 & 0x000000ff) == 0x0A) { // 发送给 APP 的指令
		uint8 idex = 0;
		for(i = 0; i < 8; i++) {
			recv_data = READ_PERI_REG(SPI_W0(SpiNum_HSPI)+(i<<2));
			tcpTxCmd_buf[idex++] = recv_data & 0xff;
			tcpTxCmd_buf[idex++] = (recv_data>>8) & 0xff;
			tcpTxCmd_buf[idex++] = (recv_data>>16) & 0xff;
			tcpTxCmd_buf[idex++] = (recv_data>>24) & 0xff;
		}
		// 只有在WiFi连接时才发送数据
		if(wifi_connected) {
			espconn_send(&PhoneConn, tcpTxCmd_buf, idex);//发送数据
			if(debugModeOpen) {
				os_printf("send to App ");
			}
		} else {
			if(debugModeOpen) {
				os_printf("WiFi not connected, data not sent ");
			}
		}
		GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0); // HSPI 可以接收新数据
	}
	else if((recv_data0 & 0x000000ff) == 0x0E) { // 发送给 ESP8266 的指令
		uint8 index = 0;
		uint8 j = 0;
		tst_Framecnt = 0;
		tcpRx_buf[index++] = (recv_data0>>16) & 0xff;
		tcpRx_buf[index++] = (recv_data0>>24) & 0xff;
		for(i = 1; i < 8; i++) {
			recv_data = READ_PERI_REG(SPI_W0(SpiNum_HSPI)+(i<<2));
			tcpRx_buf[index++] = recv_data & 0xff;
			tcpRx_buf[index++] = (recv_data>>8) & 0xff;
			tcpRx_buf[index++] = (recv_data>>16) & 0xff;
			tcpRx_buf[index++] = (recv_data>>24) & 0xff;
		}
		if(((recv_data0>>8) & 0x000000ff) == 0x05) { // 修改 ssid 和 密码
			// 更新WiFi Station配置
			for( i = 0; i < 32; i++) {
				ssid_buf[i] = tcpRx_buf[j++];
			}
			for( i = 0; i < 32; i++) {
				 password_buf[i] = tcpRx_buf[j++];
			}
			saveData2Flash();

			// 重新配置Station模式并连接
			wifi_connected = 0;
			user_set_station_config();
			wifi_station_disconnect();
			wifi_station_connect();
			os_printf("WiFi config updated, reconnecting...\n");
		}
		else if(((recv_data0>>8) & 0x000000ff) == 0x06) { // 修改 TCP 远端 ip 地址
			uint8 index = 0;
			uint32 ipAddress;
			// tcpRx_buf[index++] = (recv_data0>>16) & 0xff;
			// tcpRx_buf[index++] = (recv_data0>>24) & 0xff;
			// for(i = 1; i < 8; i++) {
				// recv_data = READ_PERI_REG(SPI_W0(SpiNum_HSPI)+(i<<2));
				// tcpRx_buf[index++] = recv_data & 0xff;
				// tcpRx_buf[index++] = (recv_data>>8) & 0xff;
				// tcpRx_buf[index++] = (recv_data>>16) & 0xff;
				// tcpRx_buf[index++] = (recv_data>>24) & 0xff;
			// }
			ipAddress = (tcpRx_buf[0]<<24) | (tcpRx_buf[1]<<16) | (tcpRx_buf[2]<<8) | tcpRx_buf[3];
			if(debugModeOpen) {
				os_printf(" from STM32 ipAddress %d",ipAddress);
			}
//			os_printf(" form STM32 ipAddress %d",ipAddress);
			my_user_tcp_init(ipAddress);
		}
		else if(((recv_data0>>8) & 0x000000ff) == 0x02) { // 开启调试串口打印
			debugModeOpen = (tcpRx_buf[4] == 0x00) ? 0x00 : 0x01;
			os_printf("debugMode=%d, ESP8266 firmware version=%d", debugModeOpen, version);
		}

	}
	//return 0;
}

// SPI interrupt callback function.
void spi_slave_isr_sta(void *para)
{
    uint32 regvalue;
    uint32 statusW, statusR, counter;
	uint32 recv_data[16]; 
	
    if (READ_PERI_REG(0x3ff00020)&BIT4) {
        //following 3 lines is to clear isr signal
        CLEAR_PERI_REG_MASK(SPI_SLAVE(SpiNum_SPI), 0x3ff);
    } else if (READ_PERI_REG(0x3ff00020)&BIT7) { //bit7 is for hspi isr,
		GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 1);
        regvalue = READ_PERI_REG(SPI_SLAVE(SpiNum_HSPI));
//        os_printf("spi_slave_isr_sta SPI_SLAVE[0x%08x]\n\r", regvalue);
        SPIIntClear(SpiNum_HSPI);
        SET_PERI_REG_MASK(SPI_SLAVE(SpiNum_HSPI), SPI_SYNC_RESET);
        SPIIntClear(SpiNum_HSPI);
        
        SPIIntEnable(SpiNum_HSPI, SpiIntSrc_WrStaDone
                 | SpiIntSrc_RdStaDone 
                 | SpiIntSrc_WrBufDone 
                 | SpiIntSrc_RdBufDone);

        if (regvalue & SPI_SLV_WR_BUF_DONE) {
			char i = 0;			
            // User can get data from the W0~W7
//           os_printf("spi_slave_isr_sta : SPI_SLV_WR_BUF_DONE\n\r");
			SPI_Receive_Data();
        }
		else if (regvalue & SPI_SLV_RD_BUF_DONE) {
            // TO DO 
//            os_printf("spi_slave_isr_sta : SPI_SLV_RD_BUF_DONE\n\r"); 
			if(tcpRxCacheCnt > 0) {
				tcpRxCacheCnt--;
			}
			else {
				tcpRxCacheCnt = 0;
			}
			if(debugModeOpen) {
				os_printf("slv_r(%d) ", tcpRxCacheCnt);
			}
			if(tcpRxCacheCnt > 0) {
				SPISlaveSendData(SpiNum_HSPI, tcpRxBuf0, 8);

				switch(tcpRxCacheCnt) {
					case 1:
						break;
					case 2: memcpy(tcpRxBuf0, tcpRxBuf1, 32);
						break;
					case 3: memcpy(tcpRxBuf0, tcpRxBuf1, 32);
						memcpy(tcpRxBuf1, tcpRxBuf2, 32);
						break;
					case 4: memcpy(tcpRxBuf0, tcpRxBuf1, 32);
						memcpy(tcpRxBuf1, tcpRxBuf2, 32);
						memcpy(tcpRxBuf2, tcpRxBuf3, 32);
						break;
					case 5: memcpy(tcpRxBuf0, tcpRxBuf1, 32);
						memcpy(tcpRxBuf1, tcpRxBuf2, 32);
						memcpy(tcpRxBuf2, tcpRxBuf3, 32);
						memcpy(tcpRxBuf3, tcpRxBuf4, 32);
						break;
					default:
						if(debugModeOpen) {
							os_printf("slv_r_over(%d) ", tcpRxCacheCnt);
						}
						break;
				}
				GPIO_OUTPUT_SET(GPIO_ID_PIN(2), 1);// GPIO2 输出高，数据读取完毕
			}
			else {
				GPIO_OUTPUT_SET(GPIO_ID_PIN(2), 0);// GPIO2 输出低，数据读取完毕
			}
			GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0); // HSPI 可以接收新数据
			
        }
        if (regvalue & SPI_SLV_RD_STA_DONE) {
            statusR = READ_PERI_REG(SPI_RD_STATUS(SpiNum_HSPI));
            statusW = READ_PERI_REG(SPI_WR_STATUS(SpiNum_HSPI));
			if(debugModeOpen) {
				os_printf("spi_slave_isr_sta : SPI_SLV_RD_STA_DONE[R=0x%08x,W=0x%08x]\n\r", statusR, statusW);
			}
			GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0); // HSPI 可以接收新数据
        }

        if (regvalue & SPI_SLV_WR_STA_DONE) {
            statusR = READ_PERI_REG(SPI_RD_STATUS(SpiNum_HSPI));
            statusW = READ_PERI_REG(SPI_WR_STATUS(SpiNum_HSPI));
			if(debugModeOpen) {
				os_printf("spi_slave_isr_sta : SPI_SLV_WR_STA_DONE[R=0x%08x,W=0x%08x]\n\r", statusR, statusW);
			}
			GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0); // HSPI 可以接收新数据
        }
        if ((regvalue & SPI_TRANS_DONE) && ((regvalue & 0xf) == 0)) {
			if(debugModeOpen) {
				os_printf("spi_slave_isr_sta : SPI_TRANS_DONE\n\r");
			}
			GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0); // HSPI 可以接收新数据
        }
        //SHOWSPIREG(SpiNum_HSPI);
    }
	
}

// Test spi slave interfaces.
void ICACHE_FLASH_ATTR spi_slave_init()
{
    //
    SpiAttr hSpiAttr;
    hSpiAttr.bitOrder = SpiBitOrder_MSBFirst;
    hSpiAttr.speed = 0;
    hSpiAttr.mode = SpiMode_Slave;
    hSpiAttr.subMode = SpiSubMode_0;

    // Init HSPI GPIO
    WRITE_PERI_REG(PERIPHS_IO_MUX, 0x105);
    PIN_FUNC_SELECT(PERIPHS_IO_MUX_MTDI_U, 2);//configure io to spi mode
    PIN_FUNC_SELECT(PERIPHS_IO_MUX_MTCK_U, 2);//configure io to spi mode
    PIN_FUNC_SELECT(PERIPHS_IO_MUX_MTMS_U, 2);//configure io to spi mode
    PIN_FUNC_SELECT(PERIPHS_IO_MUX_MTDO_U, 2);//configure io to spi mode

    os_printf("\r\n == spi init slave ==\r\n");
    SPIInit(SpiNum_HSPI, &hSpiAttr);
    
    // Set spi interrupt information.
    SpiIntInfo spiInt;
    spiInt.src = (SpiIntSrc_TransDone 
        | SpiIntSrc_WrStaDone 
        |SpiIntSrc_RdStaDone 
        |SpiIntSrc_WrBufDone 
        |SpiIntSrc_RdBufDone);
    spiInt.isrFunc = spi_slave_isr_sta;
    SPIIntCfg(SpiNum_HSPI, &spiInt);
   // SHOWSPIREG(SpiNum_HSPI);
    
    SPISlaveRecvData(SpiNum_HSPI);
    // uint32_t sndData[8] = { 0 };
    // sndData[0] = 0x35343332;
    // sndData[1] = 0x39383736;
    // sndData[2] = 0x3d3c3b3a;
    // sndData[3] = 0x11103f3e;
    // sndData[4] = 0x15141312;
    // sndData[5] = 0x19181716;
    // sndData[6] = 0x1d1c1b1a;
    // sndData[7] = 0x21201f1e;

//    SPISlaveSendData(SpiNum_HSPI, sndData, 8);
//    WRITE_PERI_REG(SPI_RD_STATUS(SpiNum_HSPI), 0x8A);
//    WRITE_PERI_REG(SPI_WR_STATUS(SpiNum_HSPI), 0x83);
}

/**
 * @brief Test spi interfaces.
 *
 */
void ICACHE_FLASH_ATTR
user_init(void)
{
	int i = 0;
	uart_init(BIT_RATE_115200,BIT_RATE_115200);
	os_printf("ESP8266 Station Mode Initialization\n");

	// 设置为Station模式
	wifi_set_opmode(STATION_MODE); // 0x01:Station、0x02:SoftAP、0x03:Station+SoftAP

	// 配置Station模式的WiFi连接参数
	user_set_station_config();

	// 初始化定时器 - 改为100ms间隔，减少系统负载
	os_timer_disarm(&test_timer);
	os_timer_setfn(&test_timer, (os_timer_func_t *)timer_callback, NULL);
	os_timer_arm(&test_timer, 100, 1);

	// 初始化WiFi连接检查定时器
	os_timer_disarm(&wifi_check_timer);
	os_timer_setfn(&wifi_check_timer, (os_timer_func_t *)wifi_check_ip, NULL);
	os_timer_arm(&wifi_check_timer, 1000, 0);

	// WiFi Station配置
	wifi_station_set_reconnect_policy(true);  // 启用自动重连
	wifi_station_set_auto_connect(1);         // 启用自动连接
	wifi_set_sleep_type(NONE_SLEEP_T);
	// 不要停止看门狗，而是定期喂狗
	// system_soft_wdt_stop();

	// 开始连接WiFi
	wifi_station_connect();
	os_printf("Connecting to WiFi...\n");

	// IO2 配置为普通输出IO，通知STM32读取数据，0无新数据,1有新数据
	PIN_FUNC_SELECT(PERIPHS_IO_MUX_GPIO2_U, FUNC_GPIO0);
	// IO4 配置为普通输出IO，通知STM32当前Busy, 0空闲, 1忙
	PIN_FUNC_SELECT(PERIPHS_IO_MUX_GPIO4_U, FUNC_GPIO0);
	// IO2 IO4 初始化
	GPIO_OUTPUT_SET(GPIO_ID_PIN(2), 0); // IO2 置低
	GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0); // IO4 置低

	// 初始化SPI从机接口
	spi_slave_init();

	os_printf("ESP8266 Station Mode Init Complete\n");
}

