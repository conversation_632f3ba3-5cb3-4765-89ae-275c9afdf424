# STM32[1]交换顺序映射方案

## 新的映射策略

将STM32[1]的32位数据以交换顺序映射到寄存器0和寄存器1中，低16位在前，高16位在后。

### 数据映射规则

```c
// STM32[1]的32位数据拆分（交换顺序）
Register[0] = STM32[1] & 0xFFFF;  // 低16位 -> 寄存器0
Register[1] = STM32[1] >> 16;     // 高16位 -> 寄存器1
```

### 具体示例

假设STM32[1] = `0x00012C15`：

```
STM32[1] = 0x00012C15
         = 0001 0010 1100 0001 0101 (二进制)
         
Register[0] = 0x2C15  (低16位，在前)
Register[1] = 0x0001  (高16位，在后)
```

泵读取到：`Register Values: 0x2C15 0x0001`

## 数据重构

泵端可以通过以下方式重构完整的32位数据：

```c
// 泵端重构STM32[1]的完整值（注意顺序）
uint32_t complete_data = ((uint32_t)register[1] << 16) | register[0];
// 结果: complete_data = 0x00012C15
```

## 预期调试输出

### 编译烧录后的输出

```
=== CUSTOM DATA MAPPING ===
STM32[0]=0x0000300A, STM32[1]=0x00012C15
Mapping STM32[1] to registers (swapped order):
  STM32[1] low 16 bits:  0x2C15 -> Register[0]
  STM32[1] high 16 bits: 0x0001 -> Register[1]
Set Register[0] = 0x2C15 (STM32[1] low)
Set Register[1] = 0x0001 (STM32[1] high)
Complete STM32[1] value: 0x00012C15

=== FINAL REGISTER CHECK ===
Register[0] = 0x2C15 (STM32[1] low 16 bits)
Register[1] = 0x0001 (STM32[1] high 16 bits)
✓ STM32[1] mapping SUCCESS: 0x2C15 0x0001 (swapped order)
Reconstructed STM32[1]: 0x00012C15 (original: 0x00012C15)
Pump will read: Register Values: 0x2C15 0x0001
```

### 泵读取结果

```
=== ESP8266 RESPONSE ===
Register Values: 0x2C15 0x0001
```

## 数据变化示例

基于之前的日志，预期的数据变化：

```
Update #1: STM32[1]=0x00012B15 -> Reg[0]=0x2B15, Reg[1]=0x0001
Update #2: STM32[1]=0x00012A15 -> Reg[0]=0x2A15, Reg[1]=0x0001
Update #3: STM32[1]=0x00012E15 -> Reg[0]=0x2E15, Reg[1]=0x0001
Update #4: STM32[1]=0x00013115 -> Reg[0]=0x3115, Reg[1]=0x0001
Update #5: STM32[1]=0x00012D15 -> Reg[0]=0x2D15, Reg[1]=0x0001
Update #6: STM32[1]=0x00012C15 -> Reg[0]=0x2C15, Reg[1]=0x0001
```

## 优势

### 1. 主要数据在前
- 寄存器0包含变化的主要数据（0x2C15）
- 寄存器1包含相对固定的高位数据（0x0001）
- 泵可以优先关注寄存器0的变化

### 2. 兼容之前的使用习惯
- 如果泵之前主要关注第一个寄存器的值
- 现在第一个寄存器包含最重要的变化数据

### 3. 灵活的数据解析
泵可以选择：
- 只使用主要数据：`register[0]`（0x2C15）
- 只使用类型标识：`register[1]`（0x0001）
- 使用完整32位：`(register[1] << 16) | register[0]`

## 数据含义分析

### 寄存器0 (主要数据)
包含变化的传感器读数（如`0x2C15`），可能表示：
- 实际传感器测量值
- 温度、压力、流量等读数
- 计数器或状态值

### 寄存器1 (类型标识)
包含相对固定的高位数据（如`0x0001`），可能表示：
- 传感器类型标识
- 数据格式版本
- 设备ID或通道号

## 对比之前的方案

### 之前的方案
```
Register Values: 0x0001 0x2C15
                 ↑      ↑
               固定值  变化值
```

### 新的方案
```
Register Values: 0x2C15 0x0001
                 ↑      ↑
               变化值  固定值
```

## 验证步骤

### 步骤1: 编译烧录
```bash
make clean && make && make flash
```

### 步骤2: 观察调试输出
确认看到：
```
Mapping STM32[1] to registers (swapped order):
  STM32[1] low 16 bits:  0x2C15 -> Register[0]
  STM32[1] high 16 bits: 0x0001 -> Register[1]
```

### 步骤3: 验证泵读取
泵应该读取到：
```
Register Values: 0x2C15 0x0001
```

### 步骤4: 数据重构测试
验证重构公式：
```c
uint32_t original = (register[1] << 16) | register[0];
// 应该等于原始的STM32[1]值
```

## 成功标志

当看到以下输出时，说明交换顺序映射成功：

```
✓ STM32[1] mapping SUCCESS: 0x2C15 0x0001 (swapped order)
Pump will read: Register Values: 0x2C15 0x0001

=== ESP8266 RESPONSE ===
Register Values: 0x2C15 0x0001
```

这表明泵现在能够以期望的顺序（低16位在前，高16位在后）获取STM32[1]的数据。
