# STM32数据处理已恢复

## 当前配置

ESP8266现在恢复为**STM32数据处理模式**：
- ✅ **STM32数据处理已启用**
- ✅ **SPI数据强制更新已恢复**
- ✅ **自定义数据映射正常工作**
- ❌ **测试数据已注释掉**

## 恢复的功能

### 1. SPI数据处理
```c
// 恢复强制更新Modbus寄存器
{
    uint32_t spi_data[8];
    for(j = 0; j < 8; j++) {
        spi_data[j] = READ_PERI_REG(SPI_W0(SpiNum_HSPI)+(j<<2));
    }
    update_modbus_from_stm32_data(spi_data, 8);
}
```

### 2. STM32数据映射
```c
// 自定义映射：从STM32[1]提取数据
uint32 stm32_data = data[1]; // 例如: 0x00017114

// 位重排逻辑
uint16 main_data = ((stm32_data & 0x0000F000) >> 12) |  // 0x7 -> 位置0
                   ((stm32_data & 0x00000F00) >> 4) |    // 0x1 -> 位置1  
                   ((stm32_data & 0x000000F0) << 4) |    // 0x1 -> 位置2
                   ((stm32_data & 0x0000000F) << 12);    // 0x4 -> 位置3

uint16 aux_data = 0x1000; // 固定值

Register[0] = main_data;  // 重排后的数据
Register[1] = aux_data;   // 固定为0x1000
```

### 3. 数据监控
```c
// 恢复数据监控定时器，每10秒检查STM32数据更新状态
data_monitor_timer_cb() // 每10秒执行一次
```

## 注释掉的功能

### 测试数据
```c
// 测试数据已注释掉
// os_printf("\n=== EXECUTING SPECIFIC RESPONSE TEST ===\n");
// test_specific_response();
```

## 预期输出

### 初始化时
```
STM32 data processing ENABLED
Waiting for real STM32 data via SPI
ESP8266 will use STM32[1] data for Modbus registers

Sensor ready! Pump can connect to 192.168.2.5:502 for Modbus TCP
Waiting for STM32 data via SPI...
Data monitor will check STM32 updates every 10 seconds
```

### SPI数据接收时
```
=== SPI RAW RECEIVED ===
recv_data0: 0x0000300A, low4bits: 0xA
=== FORCE UPDATE MODBUS ===
SPI data: 0x0000300A 0x00017114 0x00000000 0x00000000 
          0x00000000 0x00000000 0x00000000 0x00000000 

=== STM32 DATA CHANGED (Update #1) ===
=== CUSTOM DATA MAPPING ===
STM32[0]=0x0000300A, STM32[1]=0x00017114
Custom extraction from STM32[1] (0x00017114):
  Low 16 bits: 0x7114
  Rearranged:  0x4117 -> Register[0]
  Fixed value: 0x1000 -> Register[1]
Set Register[0] = 0x4117
Set Register[1] = 0x1000
Expected Raw Response data: 41 17 10 00
```

### 数据监控（每10秒）
```
=== DATA MONITOR #1 ===
STM32 data updates: 5 (new: 2)
STM32 data is updating normally
```

### 泵请求时
```
=== ESP8266 RESPONSE ===
Function Code: 0x03
Total Length: 13 bytes
Data Length: 5 bytes
Register Values: 0x4117 0x1000
Raw Response: 00 XX 00 00 00 07 01 03 04 41 17 10 00
```

## 数据流程

### 1. STM32发送数据
```
STM32 -> SPI -> ESP8266
例如: STM32[1] = 0x00017114
```

### 2. ESP8266处理数据
```
0x00017114 -> 提取0x7114 -> 重排为0x4117
Register[0] = 0x4117
Register[1] = 0x1000 (固定值)
```

### 3. 泵读取数据
```
泵请求 -> ESP8266响应: 41 17 10 00
```

## 数据变化示例

基于STM32数据的实时变化：

```
STM32[1]=0x00017114 -> 0x7114 -> 0x4117 -> Raw: 41 17 10 00
STM32[1]=0x00017215 -> 0x7215 -> 0x5217 -> Raw: 52 17 10 00
STM32[1]=0x00017316 -> 0x7316 -> 0x6317 -> Raw: 63 17 10 00
STM32[1]=0x00017417 -> 0x7417 -> 0x7417 -> Raw: 74 17 10 00
```

## 监控功能

### 数据更新检测
```c
// 检测STM32数据是否发生变化
if (data_changed) {
    data_update_count++;
    os_printf("=== STM32 DATA CHANGED (Update #%d) ===\n", data_update_count);
}
```

### 数据状态报告
```c
// 每10秒报告数据更新状态
if (data_update_count == last_data_update_count) {
    os_printf("WARNING: No STM32 data updates in last 10 seconds!\n");
} else {
    os_printf("STM32 data is updating normally\n");
}
```

## 优势

### 1. 实时数据
- 使用STM32的真实传感器数据
- 数据随STM32输入实时变化
- 反映真实的系统状态

### 2. 自动监控
- 自动检测数据更新
- 警告数据停止更新
- 提供详细的调试信息

### 3. 灵活映射
- 支持自定义数据格式
- 可以重新排列数据位
- 支持固定辅助数据

## 故障排除

### 如果没有STM32数据
```
WARNING: No STM32 data updates in last 10 seconds!
Current register values: 0x0000 0x0000
```
检查：
1. STM32是否正在发送数据
2. SPI连接是否正常
3. STM32数据格式是否正确

### 如果数据格式不正确
检查STM32发送的数据格式，确保STM32[1]包含期望的数据。

## 当前状态总结

✅ **STM32数据处理已完全恢复**
✅ **SPI数据实时更新Modbus寄存器**
✅ **自定义数据映射正常工作**
✅ **数据监控每10秒检查一次**
❌ **测试数据已注释掉，不再使用**

ESP8266现在将根据STM32的实时数据动态响应泵的请求。
