# 特定数据格式映射方案

## 用户需求

从STM32[1]=0x00017114中提取数据，期望的Raw Response格式：
```
Raw Response: 00 F3 00 00 00 07 01 03 04 41 17 10 00
                                        ↑  ↑  ↑  ↑
                                     Reg0   Reg1
```

## 数据映射分析

### 输入数据
- **STM32[1]** = `0x00017114`

### 期望输出
- **Register[0]** = `0x4117` (Raw Response: 41 17)
- **Register[1]** = `0x1000` (Raw Response: 10 00)

### 映射逻辑

从STM32[1]=0x00017114中：
1. 取低16位：`0x7114`
2. 重新排列位：`0x7114` → `0x4117`
3. 第二个寄存器固定为：`0x1000`

```c
// 位重排逻辑
uint16 main_data = ((stm32_data & 0x0000F000) >> 12) |  // 0x7 -> 位置0
                   ((stm32_data & 0x00000F00) >> 4) |    // 0x1 -> 位置1  
                   ((stm32_data & 0x000000F0) << 4) |    // 0x1 -> 位置2
                   ((stm32_data & 0x0000000F) << 12);    // 0x4 -> 位置3
// 结果: 0x7114 -> 0x4117
```

## 预期调试输出

### 编译烧录后的输出

```
=== CUSTOM DATA MAPPING ===
STM32[0]=0x0000300A, STM32[1]=0x00017114
Custom extraction from STM32[1] (0x00017114):
  Low 16 bits: 0x7114
  Rearranged:  0x4117 -> Register[0]
  Fixed value: 0x1000 -> Register[1]
Set Register[0] = 0x4117
Set Register[1] = 0x1000
Expected Raw Response data: 41 17 10 00

=== FINAL REGISTER CHECK ===
Register[0] = 0x4117 (custom mapped data)
Register[1] = 0x1000 (fixed value)
✓ Custom mapping applied
Pump will read: Register Values: 0x4117 0x1000
Raw Response will contain: 41 17 10 00
✓ Register[1] correctly set to 0x1000
```

### 泵读取结果

```
=== ESP8266 RESPONSE ===
Function Code: 0x03
Total Length: 13 bytes
Data Length: 5 bytes
Register Values: 0x4117 0x1000
Raw Response: 00 F3 00 00 00 07 01 03 04 41 17 10 00
```

## 数据变化示例

假设STM32[1]数据变化：

```
STM32[1]=0x00017114 -> 0x7114 -> 0x4117 -> Reg[0]=0x4117, Reg[1]=0x1000
STM32[1]=0x00017215 -> 0x7215 -> 0x5217 -> Reg[0]=0x5217, Reg[1]=0x1000
STM32[1]=0x00017316 -> 0x7316 -> 0x6317 -> Reg[0]=0x6317, Reg[1]=0x1000
```

## Raw Response格式详解

### 完整的Raw Response结构
```
00 F3 00 00 00 07 01 03 04 41 17 10 00
│  │  │  │  │  │  │  │  │  │  │  │  │
│  │  │  │  │  │  │  │  │  └──┴──┴──┴── 数据部分 (4字节)
│  │  │  │  │  │  │  │  └─────────────── 字节计数 (04)
│  │  │  │  │  │  │  └────────────────── 功能码 (03)
│  │  │  │  │  │  └───────────────────── 单元ID (01)
│  │  │  │  │  └──────────────────────── 长度 (07)
│  │  └──┴──┴─────────────────────────── 协议标识 (0000)
└──┴──────────────────────────────────── 事务标识 (00F3)
```

### 数据部分详解
```
41 17 10 00
│  │  │  │
│  │  │  └── Register[1]低字节 (0x00)
│  │  └───── Register[1]高字节 (0x10)
│  └──────── Register[0]低字节 (0x17)
└─────────── Register[0]高字节 (0x41)
```

## 位重排逻辑说明

### 输入: 0x7114
```
0x7114 = 0111 0001 0001 0100
         ↓    ↓    ↓    ↓
         7    1    1    4
```

### 重排: 0x4117
```
位置3: 4 -> 0100 0000 0000 0000 = 0x4000
位置2: 1 -> 0000 0001 0000 0000 = 0x0100
位置1: 1 -> 0000 0000 0001 0000 = 0x0010
位置0: 7 -> 0000 0000 0000 0111 = 0x0007
                                -------
                                0x4117
```

## 验证步骤

### 步骤1: 编译烧录
```bash
make clean && make && make flash
```

### 步骤2: 观察调试输出
确认看到：
```
Expected Raw Response data: 41 17 10 00
```

### 步骤3: 验证泵读取
泵应该读取到：
```
Register Values: 0x4117 0x1000
Raw Response: ... 41 17 10 00
```

### 步骤4: 数据一致性检查
确认Raw Response的最后4个字节是：`41 17 10 00`

## 成功标志

当看到以下输出时，说明特定格式映射成功：

```
✓ Custom mapping applied
Pump will read: Register Values: 0x4117 0x1000
Raw Response will contain: 41 17 10 00
✓ Register[1] correctly set to 0x1000

=== ESP8266 RESPONSE ===
Raw Response: 00 F3 00 00 00 07 01 03 04 41 17 10 00
```

这表明数据已按照您的特定需求正确映射和传输。
