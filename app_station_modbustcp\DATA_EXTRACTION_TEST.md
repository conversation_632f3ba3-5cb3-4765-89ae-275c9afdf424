# 数据提取测试和问题诊断

## 当前问题

用户需要的数据`0x12B15`在STM32[1]中，但泵读取到的是`0xCB13`而不是`0x12B15`。

## 数据分析

### 从SPI日志
```
SPI data: 0x0000300A 0x00012B15 0x00000000 0x00000000
```

- **STM32[0]** = `0x0000300A`
- **STM32[1]** = `0x00012B15`
- **用户需要的数据** = `0x12B15` (STM32[1]的低16位)

### 从泵读取结果
```
Register Values: 0xCB13 0x300A
```

- **寄存器0** = `0xCB13` ❌ (应该是 `0x12B15`)
- **寄存器1** = `0x300A` ✅ (正确，来自STM32[0]的低16位)

## 问题诊断

### 可能的原因

1. **数据提取错误**: `data[1] & 0xFFFF` 计算错误
2. **寄存器覆盖**: 后续代码覆盖了寄存器0的值
3. **字节序问题**: 大小端序转换问题
4. **内存问题**: 数据在传输过程中被修改

### 十六进制分析

```
STM32[1] = 0x00012B15
期望结果 = 0x12B15 (低16位)
实际结果 = 0xCB13

0x12B15 = 0001 0010 1011 0001 0101 (二进制)
0xCB13  = 1100 1011 0001 0011 (二进制)
```

看起来像是位移或字节序问题。

## 新的调试功能

我已经添加了详细的调试输出：

```c
os_printf("=== CUSTOM DATA MAPPING ===\n");
os_printf("STM32[0]=0x%08X, STM32[1]=0x%08X\n", data[0], data[1]);
os_printf("Extracting user data: STM32[1] & 0xFFFF = 0x%04X\n", user_data);
os_printf("Set Register[0] = 0x%04X (user data)\n", user_data);
os_printf("Set Register[1] = 0x%04X (aux data)\n", aux_data);
```

## 预期调试输出

编译烧录后，应该看到：

```
=== CUSTOM DATA MAPPING ===
STM32[0]=0x0000300A, STM32[1]=0x00012B15
Extracting user data: STM32[1] & 0xFFFF = 0x2B15
Set Register[0] = 0x2B15 (user data)
Set Register[1] = 0x300A (aux data)

=== FINAL REGISTER CHECK ===
Register[0] = 0x2B15 (should be user data 0x12B15)
Register[1] = 0x300A (aux data)
```

然后泵应该读取到：
```
Register Values: 0x2B15 0x300A
```

## 数据值说明

### 0x12B15 vs 0x2B15

如果STM32[1] = `0x00012B15`，那么：
- 低16位应该是 `0x2B15`，不是 `0x12B15`
- `0x12B15` 是完整的20位值
- `0x2B15` 是低16位值

### 如果需要完整的20位值

如果您需要的是完整的`0x12B15`值，我们需要不同的提取方法：

```c
// 方法1: 取低20位 (需要特殊处理)
uint32_t full_value = data[1] & 0x000FFFFF;  // 0x12B15

// 方法2: 如果数据跨越两个寄存器
uint16_t reg0 = (data[1] >> 4) & 0xFFFF;     // 右移4位得到0x12B1
uint16_t reg1 = ((data[1] & 0x0F) << 12) | other_data; // 剩余4位
```

## 验证步骤

### 步骤1: 编译烧录
```bash
make clean && make && make flash
```

### 步骤2: 观察调试输出
重点关注：
1. `STM32[1]=0x00012B15` 是否正确
2. `Extracting user data: STM32[1] & 0xFFFF = 0x????` 的结果
3. 最终寄存器值是否正确

### 步骤3: 确认数据格式
如果低16位是`0x2B15`而不是`0x12B15`，需要确认：
- 您需要的是20位值`0x12B15`还是16位值`0x2B15`？
- STM32数据格式是否正确？

## 可能的解决方案

### 如果需要20位值 0x12B15
```c
// 提取低20位，放入两个寄存器
uint32_t full_data = data[1] & 0x000FFFFF;  // 0x12B15
uint16_t reg0 = (full_data >> 4) & 0xFFFF;  // 高16位
uint16_t reg1 = (full_data & 0x0F) << 12;   // 低4位左移
```

### 如果需要16位值 0x2B15
```c
// 当前的实现应该正确
uint16_t user_data = (uint16)(data[1] & 0xFFFF);  // 0x2B15
```

请先运行新的调试版本，确认实际提取的值是什么，然后我们可以进一步调整。
