# STM32数据集成 - 下一步行动指南

## 当前状态

✅ **ESP8266 Modbus TCP功能正常工作**
- 泵能够成功读取数据：`Register Values: 0x1234 0x5678`
- 通信协议完全正常
- 使用初始测试数据：`STM32[0] = 0x12345678`

## 下一步目标

🎯 **集成STM32真实传感器数据**
- 替换测试数据为STM32实时数据
- 确保数据实时更新
- 验证数据传输稳定性

## 新增的监控功能

### 1. 数据变化检测
```
=== STM32 DATA CHANGED (Update #1) ===
=== STM32 DATA UNCHANGED ===
```

### 2. 数据状态监控
每10秒输出一次监控报告：
```
=== DATA MONITOR #1 ===
STM32 data updates: 5 (new: 2)
STM32 data is updating normally
```

### 3. 警告系统
如果STM32数据长时间不更新：
```
WARNING: No STM32 data updates in last 10 seconds!
Current register values: 0x1234 0x5678
```

## 预期的调试输出

### 情况1: STM32正常发送数据
```
=== SPI RAW RECEIVED ===
recv_data0: 0x1234567D, low4bits: 0xD
SPI data frame detected (0x0D)
=== SPI DATA RECEIVED ===
SPI raw data: 0x1234567D 0x9ABCDEF0 ...
=== STM32 DATA CHANGED (Update #1) ===
First register values for pump: 0x1234 0x567D (STM32[0]=0x1234567D)
```

### 情况2: STM32发送错误格式数据
```
=== SPI RAW RECEIVED ===
recv_data0: 0x12345678, low4bits: 0x8
SPI unknown format data, trying to update Modbus anyway
=== SPI UNKNOWN DATA ===
SPI raw data: 0x12345678 0x9ABCDEF0 ...
=== STM32 DATA CHANGED (Update #1) ===
First register values for pump: 0x1234 0x5678 (STM32[0]=0x12345678)
```

### 情况3: STM32没有发送数据
```
=== DATA MONITOR #1 ===
STM32 data updates: 0 (new: 0)
WARNING: No STM32 data updates in last 10 seconds!
Current register values: 0x1234 0x5678
```

## 行动步骤

### 步骤1: 编译并烧录新代码
```bash
make clean && make && make flash
```

### 步骤2: 监控串口输出
查看以下关键信息：
1. **初始化**: `Setting initial test data for Modbus registers`
2. **SPI数据**: `=== SPI RAW RECEIVED ===`
3. **数据更新**: `=== STM32 DATA CHANGED ===`
4. **监控报告**: `=== DATA MONITOR ===`

### 步骤3: 验证STM32连接
- 检查SPI硬件连接
- 确认STM32正在发送数据
- 验证数据格式是否正确

### 步骤4: 数据格式优化
如果STM32数据格式不符合要求：
1. **选项A**: 修改STM32代码，确保最低4位为0x0D
2. **选项B**: 使用当前的强制处理逻辑（推荐）

## 关键数据映射

### 泵读取的数据
```
泵请求: 寄存器0-1 (4字节)
ESP8266响应: STM32数据[0]的32位值
- 寄存器0 = STM32数据[0]的高16位
- 寄存器1 = STM32数据[0]的低16位
```

### STM32数据优先级
由于泵只读取前4字节，STM32应该将最重要的数据放在第一个32位数据中：
```c
stm32_data[0] = 最重要的传感器数据 (泵会读取这个)
stm32_data[1] = 次要数据
stm32_data[2] = 其他数据
...
```

## 故障排除

### 如果看不到SPI数据
1. 检查STM32是否运行
2. 检查SPI硬件连接
3. 确认STM32 SPI配置正确

### 如果数据不更新
1. 检查STM32发送频率
2. 验证数据格式
3. 查看监控报告中的更新计数

### 如果泵读取到错误数据
1. 检查数据映射逻辑
2. 验证字节序是否正确
3. 确认STM32数据[0]包含正确信息

## 成功标志

当看到以下输出时，说明STM32集成成功：
```
=== STM32 DATA CHANGED (Update #X) ===
First register values for pump: 0xXXXX 0xXXXX (STM32[0]=0xXXXXXXXX)
=== DATA MONITOR #X ===
STM32 data updates: X (new: X)
STM32 data is updating normally
```

并且泵读取到的不再是固定的`0x1234 0x5678`，而是变化的真实传感器数据。
