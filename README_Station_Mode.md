# ESP8266 Station Mode 配置说明

## 修改概述

本次修改将ESP8266从单AP模式改为单Station模式，实现WiFi连接完成后自动通过UDP发送STM端传来的数据。

## 主要修改内容

### 1. WiFi模式更改
- 从 `wifi_set_opmode(2)` (SoftAP模式) 改为 `wifi_set_opmode(STATION_MODE)` (Station模式)
- 移除了SoftAP配置函数，新增了Station配置函数

### 2. 新增WiFi连接管理
- 添加了 `wifi_check_ip()` 函数用于监控WiFi连接状态
- 添加了 `wifi_connected` 全局变量跟踪连接状态
- 实现了WiFi连接完成后自动初始化UDP连接

### 3. UDP配置优化
- 修改了 `user_udp_init()` 函数，使其根据Station模式的IP地址动态配置
- 本地IP设置为Station获取的IP地址
- 远程IP设置为网关地址（通常是路由器）

### 4. 数据发送控制
- 修改了定时器回调函数，只有在WiFi连接时才发送数据
- 修改了SPI接收数据函数，确保只有在WiFi连接时才通过UDP发送数据

### 5. 配置存储扩展
- 扩展了SSID和密码缓冲区从15字节到32字节
- 更新了Flash存储相关函数以支持更大的配置数据

## 配置方法

### 方法1：修改代码中的默认配置
在 `user_main.c` 中修改以下行：
```c
uint8 ssid_buf[32] = "YourWiFiSSID";     // 修改为要连接的WiFi SSID
uint8 password_buf[32] = "YourWiFiPassword"; // 修改为要连接的WiFi密码
```

### 方法2：通过SPI接口动态配置
STM32可以通过SPI发送配置命令来更新WiFi设置：
- 命令格式：0x0E05 + SSID(32字节) + Password(32字节)
- ESP8266会将新配置保存到Flash并重新连接WiFi

## 工作流程

1. **初始化阶段**
   - ESP8266启动并设置为Station模式
   - 读取Flash中的WiFi配置（如果存在）
   - 开始连接WiFi网络

2. **连接阶段**
   - `wifi_check_ip()` 函数定期检查连接状态
   - 连接成功后设置 `wifi_connected = 1`
   - 初始化UDP连接

3. **数据传输阶段**
   - STM32通过SPI发送数据到ESP8266
   - ESP8266接收到数据后，如果WiFi已连接，立即通过UDP发送
   - 支持大数据包的分片传输

## 调试信息

启用调试模式后，可以看到以下信息：
- WiFi连接状态变化
- UDP连接配置信息
- 数据发送状态

## 编译修复

在修改过程中修复了以下编译问题：
1. 添加了函数声明以避免隐式声明错误
2. 修正了静态函数声明与定义的匹配问题
3. 替换了IP2STR宏为标准IP地址打印方式
4. 确保所有字符串函数使用os_strlen而不是strlen

## 注意事项

1. 确保WiFi网络的SSID和密码正确配置
2. 网络环境需要支持UDP通信
3. 如果WiFi连接失败，ESP8266会自动重试连接
4. 数据只有在WiFi连接成功后才会发送，避免数据丢失
5. 编译时确保使用正确的ESP8266 NONOS SDK环境

## 兼容性

此修改保持了与STM32的SPI通信协议兼容，STM32端代码无需修改。

## 编译说明

使用ESP8266 NONOS SDK的标准编译流程：
```bash
cd appmy
make clean
make
```

确保编译环境中包含xtensa-lx106-elf工具链。
