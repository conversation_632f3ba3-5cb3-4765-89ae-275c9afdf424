# 特定响应测试功能

## 测试目的

测试ESP8266是否能够发送特定的Modbus响应数据：`01 03 04 02 26 00 00`

## 测试数据分析

### 期望的响应格式
```
01 03 04 02 26 00 00
│  │  │  │  │  │  │
│  │  │  │  │  │  └── Register[1]低字节 (0x00)
│  │  │  │  │  └───── Register[1]高字节 (0x00)
│  │  │  │  └──────── Register[0]低字节 (0x26)
│  │  │  └─────────── Register[0]高字节 (0x02)
│  │  └────────────── 字节计数 (04 = 4字节数据)
│  └───────────────── 功能码 (03 = 读保持寄存器)
└──────────────────── 单元ID (01)
```

### 对应的寄存器值
- **Register[0]** = `0x0226` (十进制: 550)
- **Register[1]** = `0x0000` (十进制: 0)

## 测试函数

### test_specific_response()
这个函数会：
1. 设置寄存器0为0x0226
2. 设置寄存器1为0x0000
3. 验证设置是否正确
4. 输出期望的响应格式

### test_response_timer_cb()
定时器回调函数，每30秒执行一次测试（如果启用）。

## 预期调试输出

### 初始化时的输出
```
=== EXECUTING SPECIFIC RESPONSE TEST ===
=== TEST SPECIFIC RESPONSE ===
Setting registers to produce response: 01 03 04 02 26 00 00
Set Register[0] = 0x0226 (decimal: 550)
Set Register[1] = 0x0000 (decimal: 0)
Verification:
  Register[0] = 0x0226
  Register[1] = 0x0000
Expected Modbus response (without MBAP header):
  Unit ID:     01
  Function:    03
  Byte count:  04
  Reg[0] high: 02
  Reg[0] low:  26
  Reg[1] high: 00
  Reg[1] low:  00
Complete response: 01 03 04 02 26 00 00
✓ Test data set successfully!
✓ Pump should receive: 01 03 04 02 26 00 00
```

### 泵请求时的响应
当泵发送Modbus请求时，ESP8266应该响应：
```
=== ESP8266 RESPONSE ===
Function Code: 0x03
Total Length: 13 bytes
Data Length: 5 bytes
Register Values: 0x0226 0x0000
Raw Response: 00 XX 00 00 00 07 01 03 04 02 26 00 00
                                        ↑  ↑  ↑  ↑  ↑  ↑  ↑
                                        期望的7字节数据
```

## 使用方法

### 方法1: 自动执行（当前实现）
测试会在ESP8266初始化完成后自动执行一次。

### 方法2: 定期执行（可选）
如果需要定期测试，可以启用定时器：

```c
// 在WiFi连接成功后添加以下代码
os_timer_disarm(&test_timer);
os_timer_setfn(&test_timer, (os_timer_func_t *)test_response_timer_cb, NULL);
os_timer_arm(&test_timer, 30000, 1); // 30秒间隔，重复执行
```

## 验证步骤

### 步骤1: 编译烧录
```bash
make clean && make && make flash
```

### 步骤2: 观察初始化输出
确认看到：
```
✓ Test data set successfully!
✓ Pump should receive: 01 03 04 02 26 00 00
```

### 步骤3: 测试泵连接
当泵连接并发送Modbus请求时，检查响应是否包含：
```
Raw Response: ... 01 03 04 02 26 00 00
```

### 步骤4: 验证寄存器值
确认泵读取到的寄存器值：
```
Register Values: 0x0226 0x0000
```

## 数据含义

### Register[0] = 0x0226 (550)
这个值可能表示：
- 传感器读数：550个单位
- 温度：55.0°C（如果小数点后1位）
- 压力：5.50kPa（如果小数点后2位）
- 计数值：550次

### Register[1] = 0x0000 (0)
这个值可能表示：
- 状态标志：正常状态
- 错误代码：无错误
- 辅助数据：未使用
- 保留字段：固定为0

## 故障排除

### 如果测试输出显示失败
```
✗ Test data setting failed!
```
检查：
1. Modbus寄存器初始化是否正确
2. 寄存器读写函数是否正常工作

### 如果泵读取到错误数据
1. 检查Modbus TCP通信是否正常
2. 验证字节序是否正确
3. 确认寄存器映射逻辑

### 如果Raw Response格式不正确
1. 检查Modbus协议实现
2. 验证MBAP头部是否正确
3. 确认PDU格式是否符合标准

## 成功标志

当看到以下输出时，说明测试成功：

```
✓ Test data set successfully!
✓ Pump should receive: 01 03 04 02 26 00 00

=== ESP8266 RESPONSE ===
Register Values: 0x0226 0x0000
Raw Response: 00 XX 00 00 00 07 01 03 04 02 26 00 00
```

这表明ESP8266能够正确发送您期望的特定响应数据。
