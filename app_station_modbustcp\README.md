# ESP8266 Modbus TCP 实现

这个项目将原有的TCP通信修改为标准的Modbus TCP协议实现，使ESP8266能够作为Modbus TCP从站设备工作。

## 功能特性

### 支持的Modbus功能码
- **0x01** - 读取线圈状态 (Read Coils)
- **0x02** - 读取离散输入状态 (Read Discrete Inputs)
- **0x03** - 读取保持寄存器 (Read Holding Registers)
- **0x04** - 读取输入寄存器 (Read Input Registers)
- **0x05** - 写单个线圈 (Write Single Coil)
- **0x06** - 写单个寄存器 (Write Single Register)
- **0x0F** - 写多个线圈 (Write Multiple Coils)
- **0x10** - 写多个寄存器 (Write Multiple Registers)

### 数据映射
- **保持寄存器 (40001-41000)**: 可读写，写入时会自动发送到STM32
- **输入寄存器 (30001-31000)**: 只读，从STM32接收的数据会自动更新到这些寄存器
- **线圈 (00001-01000)**: 可读写
- **离散输入 (10001-11000)**: 只读

### STM32数据交互
- STM32通过SPI发送的数据会自动映射到Modbus输入寄存器
- 通过Modbus写入保持寄存器的数据会自动发送到STM32
- 32位STM32数据会拆分为两个16位Modbus寄存器存储

## 网络配置

- **协议**: Modbus TCP
- **端口**: 502 (标准Modbus TCP端口)
- **工作模式**: TCP服务器模式（从站）
- **WiFi模式**: Station模式，连接泵的WiFi AP
- **泵WiFi名称**: USR-W610_A018 (无密码)
- **泵WiFi地址**: *************
- **网关地址**: ***********
- **传感器固定IP**: ***********

## 编译和烧录

### 环境要求
- ESP8266 NONOS SDK (已测试版本: 3.0+)
- xtensa-lx106-elf 交叉编译工具链
- Linux/Unix 环境 (推荐) 或 WSL

### 编译步骤
1. 确保ESP8266 NONOS SDK环境已正确配置
2. 进入项目目录：
   ```bash
   cd app_station_modbustcp
   ```
3. 编译项目：
   ```bash
   make clean
   make
   ```
4. 烧录到ESP8266：
   ```bash
   make flash
   ```

### 编译问题解决
如果遇到编译错误，请检查：
1. **ip_addr_t 未定义错误**: 确保在包含espconn.h之前包含了ip_addr.h
2. **头文件路径错误**: 检查SDK路径配置是否正确
3. **交叉编译工具链**: 确保xtensa-lx106-elf-gcc在PATH中

## 泵连接配置

泵设备可以通过以下参数连接到传感器：

连接参数：
- IP地址: ***********
- 端口: 502
- 单元ID: 1
- 协议: Modbus TCP

## 寄存器地址映射

### STM32传感器数据 - 同时存储在两种寄存器类型中
STM32通过SPI发送的数据会自动映射到两种寄存器类型：

#### 输入寄存器 (功能码 0x04)
- 地址 0-15: STM32 SPI数据 (8个32位数据拆分为16个16位寄存器)
- 地址 0: STM32数据[0]的高16位
- 地址 1: STM32数据[0]的低16位
- 地址 2: STM32数据[1]的高16位
- 地址 3: STM32数据[1]的低16位
- 地址 4: STM32数据[2]的高16位
- 地址 5: STM32数据[2]的低16位
- ...以此类推到地址15

#### 保持寄存器 (功能码 0x03) - 相同数据的镜像
- 地址 0-15: 与输入寄存器相同的STM32数据
- 地址 0-15: 与输入寄存器完全相同的数据映射
- 泵可以使用功能码0x03读取这些数据

#### 保持寄存器 (功能码 0x06/0x10) - 控制命令
- 地址 0-15: **只读区域** - STM32传感器数据（与输入寄存器相同）
- 地址 16-999: **可写区域** - 控制寄存器
- 写入地址16-999时会自动发送到STM32 (每两个16位寄存器组合成一个32位数据)
- 用于向STM32发送控制命令或配置参数
- **注意**: 尝试写入地址0-15会返回异常码0x02（非法数据地址）

## 调试

### 串口调试信息
ESP8266会通过串口输出调试信息，包括：
- WiFi连接状态
- Modbus请求和响应
- SPI数据交互
- 错误信息

### 调试模式
可以通过SPI命令或Modbus寄存器启用详细调试输出。

## 异常处理

实现了完整的Modbus异常响应：
- 0x01: 非法功能码
- 0x02: 非法数据地址
- 0x03: 非法数据值
- 0x04: 从站设备故障

## 注意事项

1. ESP8266作为Modbus TCP从站，需要主站设备主动发起连接
2. 支持多个并发连接，但建议单个主站连接以获得最佳性能
3. SPI通信与STM32保持原有协议，确保兼容性
4. WiFi配置可以通过SPI命令动态修改
5. 系统会定期喂看门狗，防止重启

## 故障排除

### 连接问题
- 检查WiFi配置是否正确
- 确认ESP8266和主站在同一网络
- 检查防火墙设置

### 通信问题
- 使用串口监控调试信息
- 检查Modbus请求格式是否正确
- 确认寄存器地址在有效范围内

### SPI通信问题
- 检查STM32和ESP8266的SPI连接
- 确认SPI时序配置正确
- 监控GPIO2和GPIO4的状态信号
