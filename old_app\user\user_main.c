/*
 * ESPRSSIF MIT License
 *
 * Copyright (c) 2016 <ESPRESSIF SYSTEMS (SHANGHAI) PTE LTD>
 *
 * Permission is hereby granted for use on ESPRESSIF SYSTEMS ESP8266 only, in which case,
 * it is free of charge, to any person obtaining a copy of this software and associated
 * documentation files (the "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the Software is furnished
 * to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all copies or
 * substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
 * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WH<PERSON>HER
 * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 *
 */

#include "osapi.h"
#include "ets_sys.h"
#include "at_custom.h"
#include "user_interface.h"
#include "espconn.h"
#include "driver/uart.h"
#include "driver/uart_register.h"
#include "driver/key.h"

struct espconn PhoneConn;
esp_udp PhoneConnUdp;

LOCAL os_timer_t timer;//声明定时器timer

// test :AT+TEST=1,"abc"<,3>
void ICACHE_FLASH_ATTR
at_setupCmdTest(uint8_t id, char *pPara)
{
    int result = 0, err = 0, flag = 0;
    uint8 buffer[32] = {0};
    pPara++; // skip '='

    //get the first parameter
    // digit
    flag = at_get_next_int_dec(&pPara, &result, &err);

    // flag must be ture because there are more parameter
    if (flag == FALSE) {
        at_response_error();
        return;
    }

    if (*pPara++ != ',') { // skip ','
        at_response_error();
        return;
    }

    os_sprintf(buffer, "the first parameter:%d\r\n", result);
    at_port_print(buffer);

    //get the second parameter
    // string
    at_data_str_copy(buffer, &pPara, 10);
    at_port_print("the second parameter:");
    at_port_print(buffer);
    at_port_print("\r\n");

    if (*pPara == ',') {
        pPara++; // skip ','
        result = 0;
        //there is the third parameter
        // digit
        flag = at_get_next_int_dec(&pPara, &result, &err);
        // we donot care of flag
        os_sprintf(buffer, "the third parameter:%d\r\n", result);
        at_port_print(buffer);
    }

    if (*pPara != '\r') {
        at_response_error();
        return;
    }

    at_response_ok();
}

void ICACHE_FLASH_ATTR
at_testCmdTest(uint8_t id)
{
    uint8 buffer[32] = {0};

    os_sprintf(buffer, "%s\r\n", "at_testCmdTest");
    at_port_print(buffer);
    at_response_ok();
}

void ICACHE_FLASH_ATTR
at_queryCmdTest(uint8_t id)
{
    uint8 buffer[32] = {0};

    os_sprintf(buffer, "%s\r\n", "at_queryCmdTest");
    at_port_print(buffer);
    at_response_ok();
}

void ICACHE_FLASH_ATTR
at_exeCmdTest(uint8_t id)
{
    uint8 buffer[32] = {0};

    os_sprintf(buffer, "%s\r\n", "at_exeCmdTest");
    at_port_print(buffer);
    at_response_ok();
}

extern void at_exeCmdCiupdate(uint8_t id);
at_funcationType at_custom_cmd[] = {
    {"+TEST", 5, at_testCmdTest, at_queryCmdTest, at_setupCmdTest, at_exeCmdTest},
#ifdef AT_UPGRADE_SUPPORT
    {"+CIUPDATE", 9,       NULL,            NULL,            NULL, at_exeCmdCiupdate}
#endif
};

/******************************************************************************
 * FunctionName : user_rf_cal_sector_set
 * Description  : SDK just reversed 4 sectors, used for rf init data and paramters.
 *                We add this function to force users to set rf cal sector, since
 *                we don't know which sector is free in user's application.
 *                sector map for last several sectors : ABBBCDDD
 *                A : rf cal
 *                B : at parameters
 *                C : rf init data
 *                D : sdk parameters
 * Parameters   : none
 * Returns      : rf cal sector
*******************************************************************************/
uint32 ICACHE_FLASH_ATTR
user_rf_cal_sector_set(void)
{
    enum flash_size_map size_map = system_get_flash_size_map();
    uint32 rf_cal_sec = 0;

    switch (size_map) {
        case FLASH_SIZE_4M_MAP_256_256:
            rf_cal_sec = 128 - 8;
            break;

        case FLASH_SIZE_8M_MAP_512_512:
            rf_cal_sec = 256 - 5;
            break;

        case FLASH_SIZE_16M_MAP_512_512:
        case FLASH_SIZE_16M_MAP_1024_1024:
            rf_cal_sec = 512 - 5;
            break;

        case FLASH_SIZE_32M_MAP_512_512:
        case FLASH_SIZE_32M_MAP_1024_1024:
            rf_cal_sec = 1024 - 5;
            break;

        default:
            rf_cal_sec = 0;
            break;
    }

    return rf_cal_sec;
}

void ICACHE_FLASH_ATTR
user_rf_pre_init(void)
{
}
void timer_callback()
{
	char mod = 0;
 char buf[64] = {0};
//uart0_sendStr("hello");
 uart_tx_one_char(UART0,  '0');
  uart_tx_one_char(UART0,  '0');
   uart_tx_one_char(UART0,  '0');
    uart_tx_one_char(UART0,  '0');
	 mod = wifi_get_opmode_default();
	 uart_tx_one_char(UART0, mod + '0');
//uart1_tx_buffer("uart1 tx", 8);
	if(GPIO_INPUT_GET(GPIO_ID_PIN(0))==0)
	{
//		GPIO_OUTPUT_SET(GPIO_ID_PIN(0), 1);
	}
else{
//	GPIO_OUTPUT_SET(GPIO_ID_PIN(0), 0);
}

}

/******************************************************************************
 * FunctionName : user_set_softap_config
 * Description  : set SSID and password of ESP8266 softAP
 * Parameters   : none
 * Returns      : none
*******************************************************************************/
void ICACHE_FLASH_ATTR
user_set_softap_config(void)
{
   struct softap_config config;
 
   wifi_softap_get_config(&config); // Get config first.
    
   os_memset(config.ssid, 0, 32);
   os_memset(config.password, 0, 64);
   os_memcpy(config.ssid, "ESP8266", 7);
   os_memcpy(config.password, "12345678", 8);
   config.authmode = AUTH_WPA_WPA2_PSK;
   config.ssid_len = 0;// or its actual length
   config.max_connection = 4; // how many stations can connect to ESP8266 softAP at most.
 
   wifi_softap_set_config(&config);// Set ESP8266 softap config .
    
}

char data_buf[64];
void ICACHE_FLASH_ATTR
udpclient_recv(void *arg, char *pdata, unsigned short len)
{
	if(pdata[0]=='L'&&pdata[1]=='E'&&pdata[2]=='D'&&pdata[3]=='='&&pdata[4]=='0')
	{
//		GPIO_OUTPUT_SET(GPIO_ID_PIN(0), 0);//GPIO14输出低电平
		espconn_sent(&PhoneConn, "Pin0 Set 1", 14);//发送数据
//		espconn_sent(&PhoneConn, data_buf, 16);//发送数据
	}

	if(pdata[0]=='L'&&pdata[1]=='E'&&pdata[2]=='D'&&pdata[3]=='='&&pdata[4]=='1')
	{
//		GPIO_OUTPUT_SET(GPIO_ID_PIN(0), 1);//GPIO14输出高电平
		espconn_sent(&PhoneConn, "Pin0 Set 1", 14);//发送数据
	}
}


void ICACHE_FLASH_ATTR
user_init(void)
{
int i = 0;
uint8 opmode;
   char buf[64] = {0};
   // at_customLinkMax = 5;
   // at_init();
   // os_sprintf(buf,"compile time:%s %s",__DATE__,__TIME__);
   // at_set_custom_info(buf);
   // at_port_print("\r\nready\r\n");
   // at_cmd_array_regist(&at_custom_cmd[0], sizeof(at_custom_cmd)/sizeof(at_custom_cmd[0]));
 	uart_init(BIT_RATE_115200,BIT_RATE_115200);
	wifi_set_opmode(0x02);//设置模块为AP模式

	user_set_softap_config();//设置AP的WIFI名称及密码

	PhoneConn.type = ESPCONN_UDP;
	PhoneConn.proto.udp = &PhoneConnUdp;
	PhoneConn.proto.udp->local_port = 8888;//本地端口
	PhoneConn.proto.udp->remote_port = 8888;//远程端口
	PhoneConn.proto.udp->local_ip[0] = 255;
	PhoneConn.proto.udp->local_ip[1] = 255;
	PhoneConn.proto.udp->local_ip[2] = 255;
	PhoneConn.proto.udp->local_ip[3] = 255;

	PhoneConn.proto.udp->remote_ip[0] = 255;
	PhoneConn.proto.udp->remote_ip[1] = 255;
	PhoneConn.proto.udp->remote_ip[2] = 255;
	PhoneConn.proto.udp->remote_ip[3] = 255;

	espconn_regist_recvcb(&PhoneConn, udpclient_recv); // 注册一个UDP数据包接收回调
	espconn_create(&PhoneConn);//建立 UDP 传输 



//PIN_FUNC_SELECT(PERIPHS_IO_MUX_GPIO0_U, FUNC_GPIO0);
	// os_timer_disarm(&timer);//关闭定时器，相当于清零计时器计数
	// os_timer_setfn(&timer, (os_timer_func_t *)timer_callback, NULL);//初始化定时器
	// os_timer_arm(&timer, 1000, 1);//开始定时器计数,1000毫秒后，会调用前面的callback函数 （后面的0表示只运行一次 为1表示循环运行）

for(i = 0; i<64; i++)
{
	 data_buf[i] = i;
}



}
