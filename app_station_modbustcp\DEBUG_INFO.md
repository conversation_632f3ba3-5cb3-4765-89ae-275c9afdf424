# ESP8266 Modbus TCP 调试信息说明

本文档说明了ESP8266与泵之间通信的调试信息格式和含义。

## 调试信息类别

ESP8266会输出以下几类调试信息：

### 1. SPI数据接收

当ESP8266从STM32接收到SPI数据时，会输出：

```
=== SPI DATA RECEIVED ===
SPI raw data: 0x12345678 0x9ABCDEF0 0x11223344 0x55667788
              0xAABBCCDD 0xEEFF0011 0x22334455 0x66778899
```

**含义**：
- 这是STM32通过SPI发送给ESP8266的8个32位数据
- 每个32位数据将被拆分为两个16位Modbus寄存器

### 2. STM32数据更新

当ESP8266将STM32数据更新到Modbus寄存器时，会输出：

```
=== STM32 DATA UPDATE ===
Updated 16 Modbus registers from STM32 data
Raw 32-bit values: 0x12345678 0x9ABCDEF0 0x11223344 0x55667788
                  0xAABBCCDD 0xEEFF0011 0x22334455 0x66778899
16-bit registers: 0x1234 0x5678 0x9ABC 0xDEF0 0x1122 0x3344 0x5566 0x7788
                 0xAABB 0xCCDD 0xEEFF 0x0011 0x2233 0x4455 0x6677 0x8899
Register mapping:
  STM32[0]=0x12345678 -> Reg[0]=0x1234, Reg[1]=0x5678
  STM32[1]=0x9ABCDEF0 -> Reg[2]=0x9ABC, Reg[3]=0xDEF0
  STM32[2]=0x11223344 -> Reg[4]=0x1122, Reg[5]=0x3344
  STM32[3]=0x55667788 -> Reg[6]=0x5566, Reg[7]=0x7788
  STM32[4]=0xAABBCCDD -> Reg[8]=0xAABB, Reg[9]=0xCCDD
  STM32[5]=0xEEFF0011 -> Reg[10]=0xEEFF, Reg[11]=0x0011
  STM32[6]=0x22334455 -> Reg[12]=0x2233, Reg[13]=0x4455
  STM32[7]=0x66778899 -> Reg[14]=0x6677, Reg[15]=0x8899
```

**含义**：
- 显示STM32原始32位数据
- 显示拆分后的16位寄存器值
- 显示详细的数据映射关系

### 3. 泵连接状态

当泵连接到ESP8266时，会输出：

```
=== PUMP CONNECTED ===
Pump IP: *************:xxxxx
Local IP: ***********:502
Ready to receive Modbus commands from pump
```

当泵断开连接时，会输出：

```
=== PUMP DISCONNECTED ===
Pump IP: *************:xxxxx disconnected
Waiting for pump to reconnect...
```

### 4. 泵发送的命令

当泵发送Modbus请求时，会输出：

```
=== PUMP COMMAND ===
Transaction ID: 123
Function Code: 0x03
Request Length: 12 bytes
Start Address: 0
Quantity: 16 registers
Expected Data: 32 bytes
Raw Request: 00 7B 00 00 00 06 01 03 00 00 00 10
```

**含义**：
- Transaction ID: Modbus TCP事务ID
- Function Code: Modbus功能码（0x03=读保持寄存器）
- Start Address: 起始寄存器地址
- Quantity: 请求的寄存器数量
- Raw Request: 原始请求数据（十六进制）

### 5. ESP8266响应

当ESP8266响应泵的请求时，会输出：

```
=== ESP8266 RESPONSE ===
Function Code: 0x03
Total Length: 39 bytes
Data Length: 33 bytes
Register Values: 0x1234 0x5678 0x9ABC 0xDEF0 0x1122 0x3344 0x5566 0x7788
                 0xAABB 0xCCDD 0xEEFF 0x0011 0x2233 0x4455 0x6677 0x8899
Raw Response: 00 7B 00 00 00 27 01 03 20 12 34 56 78 9A BC DE F0 11 22 33 44 55 66 77 88
              AA BB CC DD EE FF 00 11 22 33 44 55 66 77 88 99
```

**含义**：
- Function Code: Modbus功能码
- Total Length: 总响应长度（字节）
- Data Length: 数据部分长度（字节）
- Register Values: 返回的寄存器值
- Raw Response: 原始响应数据（十六进制）

## 调试信息分析

### 完整数据传输示例

当泵请求16个寄存器（完整32字节数据）时，应该看到：

1. 泵请求：
```
=== PUMP COMMAND ===
Function Code: 0x03
Start Address: 0
Quantity: 16 registers
```

2. ESP8266响应：
```
=== ESP8266 RESPONSE ===
Function Code: 0x03
Total Length: 39 bytes
Data Length: 33 bytes
Register Values: 0x1234 0x5678 ... (16个寄存器值)
```

### 部分数据传输示例

当泵只请求部分寄存器时，例如只请求2个寄存器：

1. 泵请求：
```
=== PUMP COMMAND ===
Function Code: 0x03
Start Address: 0
Quantity: 2 registers
```

2. ESP8266响应：
```
=== ESP8266 RESPONSE ===
Function Code: 0x03
Total Length: 11 bytes
Data Length: 5 bytes
Register Values: 0x1234 0x5678
```

## 故障排除

### 如果看不到"PUMP COMMAND"

- 检查泵是否已连接到ESP8266
- 确认网络配置正确
- 检查泵是否发送了Modbus请求

### 如果看到"Quantity: 2 registers"而不是"Quantity: 16 registers"

- 泵只请求了2个寄存器，而不是完整的16个
- 需要修改泵的Modbus客户端配置，请求16个寄存器

### 如果寄存器值全为0或异常值

- 检查STM32是否正确发送数据
- 确认SPI通信正常
- 查看"SPI DATA RECEIVED"部分的原始数据
