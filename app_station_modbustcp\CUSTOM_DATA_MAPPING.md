# 自定义数据映射方案

## 问题描述

用户需要传输的实际数据是`0x1D590`，但这个数据位于STM32[1]的低16位中：

```
STM32[0]=0x0100300A -> Reg[0]=0x0100, Reg[1]=0x300A  (泵读取这个)
STM32[1]=0x0001D590 -> Reg[2]=0x0001, Reg[3]=0xD590  (用户需要的数据在这里)
```

**问题**：泵只读取寄存器0-1，无法获取到用户需要的数据`0x1D590`。

## 解决方案

### 自定义数据映射

重新设计数据映射逻辑，将用户需要的数据放在泵能读取的位置：

```c
// 寄存器0: STM32[1]的低16位 (用户需要的数据)
Register[0] = STM32[1] & 0xFFFF;  // 0x1D590

// 寄存器1: STM32[0]的低16位 (辅助数据)
Register[1] = STM32[0] & 0xFFFF;  // 0x300A
```

### 新的数据流程

1. **STM32发送数据**：
   ```
   STM32[0] = 0x0100300A
   STM32[1] = 0x0001D590  ← 包含用户需要的数据
   ```

2. **ESP8266自定义映射**：
   ```
   Register[0] = 0x1D590  ← 用户数据 (STM32[1]低16位)
   Register[1] = 0x300A   ← 辅助数据 (STM32[0]低16位)
   ```

3. **泵读取数据**：
   ```
   泵请求寄存器0-1
   ESP8266响应: 0x1D590 0x300A
   泵获得用户需要的数据: 0x1D590
   ```

## 预期输出

### 调试信息
```
=== CUSTOM DATA MAPPING ===
User data (STM32[1] low): 0x1D590 -> Register[0]
Aux data (STM32[0] low): 0x300A -> Register[1]

=== STM32 DATA UPDATE ===
Pump will read: Reg[0]=0x1D590 (user data), Reg[1]=0x300A (aux data)
User data source: STM32[1]=0x0001D590 (low 16 bits = 0x1D590)
```

### 泵读取结果
```
=== ESP8266 RESPONSE ===
Register Values: 0x1D590 0x300A
```

**重要**：泵现在能够在寄存器0中读取到用户需要的数据`0x1D590`！

## 数据含义

### 用户数据 (0x1D590)
- **十进制值**: 120208
- **可能含义**: 传感器读数、温度值、压力值等
- **位置**: 寄存器0（泵的主要读取目标）

### 辅助数据 (0x300A)
- **十进制值**: 12298
- **可能含义**: 状态信息、校验值、时间戳等
- **位置**: 寄存器1（泵的次要读取目标）

## 优势

1. **兼容性**: 泵代码无需修改，继续读取寄存器0-1
2. **灵活性**: 可以选择将最重要的数据放在寄存器0
3. **扩展性**: 其他STM32数据仍然映射到寄存器2及以后

## 验证步骤

### 步骤1: 编译烧录
```bash
make clean && make && make flash
```

### 步骤2: 观察调试输出
查看是否出现：
```
=== CUSTOM DATA MAPPING ===
User data (STM32[1] low): 0x1D590 -> Register[0]
```

### 步骤3: 确认泵读取结果
泵应该读取到：
```
Register Values: 0x1D590 0x300A
```

而不是之前的：
```
Register Values: 0x0100 0x300A
```

## 数据格式建议

### STM32端优化
建议STM32将最重要的数据放在STM32[1]的低16位：

```c
// STM32发送数据格式建议
uint32_t stm32_data[8];
stm32_data[0] = (status << 16) | auxiliary_data;     // 状态和辅助数据
stm32_data[1] = (reserved << 16) | main_sensor_data; // 主要传感器数据
```

### 数据范围
- **0x1D590 (120208)**: 如果是温度，可能表示120.208°C
- **0x300A (12298)**: 如果是压力，可能表示122.98kPa

## 成功标志

当看到以下输出时，说明自定义映射成功：

```
=== CUSTOM DATA MAPPING ===
User data (STM32[1] low): 0x1D590 -> Register[0]

=== ESP8266 RESPONSE ===
Register Values: 0x1D590 0x300A
```

这表明泵现在能够在寄存器0中读取到用户真正需要的数据`0x1D590`。
