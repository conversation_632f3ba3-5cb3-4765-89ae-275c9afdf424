# Modbus TCP 响应问题修复

## 问题描述

ESP8266没有正确响应泵的Modbus TCP请求，持续发送STM32 SPI数据，导致通信混乱。

从日志可以看出：
- 泵正在发送Modbus TCP请求（功能码0x03，读取保持寄存器）
- ESP8266正在发送响应，但可能数据不正确
- ESP8266同时在处理SPI数据，可能造成冲突

## 根本原因

1. **TCP接收回调函数混合处理**: `modbus_tcp_recv`函数同时处理Modbus TCP请求和SPI数据
2. **寄存器类型不匹配**: 泵使用功能码0x03读取保持寄存器，但STM32数据只存储在输入寄存器中
3. **数据区域冲突**: 控制命令可能覆盖STM32传感器数据

## 修复方案

### 1. 分离TCP和SPI数据处理

**修复前**:
```c
void modbus_tcp_recv(void *arg, char *data, unsigned short len)
{
    modbus_tcp_process_request(arg, data, len);
    
    // 错误：同时处理SPI数据
    if (len >= 32) {
        // SPI数据处理逻辑...
    }
}
```

**修复后**:
```c
void modbus_tcp_recv(void *arg, char *data, unsigned short len)
{
    // 只处理Modbus TCP请求，SPI数据在SPI中断中处理
    modbus_tcp_process_request(arg, data, len);
}
```

### 2. 双重寄存器映射

STM32数据同时存储在输入寄存器和保持寄存器中：

```c
// 同时更新两种寄存器类型
modbus_set_input_register(reg_addr, (uint16)(data[i] >> 16));
modbus_set_holding_register(reg_addr, (uint16)(data[i] >> 16));
```

### 3. 保护STM32数据区域

防止外部写入覆盖STM32传感器数据：

```c
// 保护地址0-15，不允许外部写入
if (addr < 16) {
    return MODBUS_EXCEPTION_ILLEGAL_DATA_ADDRESS;
}
```

## 修复后的寄存器映射

### STM32传感器数据（地址0-15）
- **输入寄存器0x04**: 可读取STM32数据
- **保持寄存器0x03**: 可读取相同的STM32数据（镜像）
- **写保护**: 不允许通过0x06/0x10写入

### 控制命令区域（地址16-999）
- **保持寄存器0x03**: 可读取控制命令
- **保持寄存器0x06/0x10**: 可写入控制命令，自动发送到STM32

## 数据流程

### 正常工作流程
1. **STM32 → ESP8266**: SPI中断接收传感器数据
2. **数据映射**: 更新Modbus寄存器地址0-15（输入寄存器和保持寄存器）
3. **泵读取**: 使用功能码0x03读取保持寄存器0-15获取传感器数据
4. **泵控制**: 使用功能码0x06/0x10写入保持寄存器16+发送控制命令

### 预期日志输出
```
Modbus TCP Sensor Server listening on ***********:502
Modbus TCP client connected from *************:xxxx
Modbus request: TID=123, FC=0x03, Len=12
Modbus response sent: FC=0x03, Len=13
```

## 测试验证

### 1. 验证传感器数据读取
泵读取地址0-15应该获得STM32的实时传感器数据。

### 2. 验证写保护
尝试写入地址0-15应该返回异常码0x02。

### 3. 验证控制命令
写入地址16+应该成功并发送到STM32。

## 故障排除

如果仍有问题：

1. **检查SPI数据更新**: 确认STM32正在发送数据
2. **监控寄存器值**: 检查地址0-15的寄存器值是否更新
3. **验证Modbus请求**: 确认泵请求的地址范围
4. **检查网络连接**: 确认TCP连接稳定

## 重要提醒

- STM32数据区域（0-15）现在是写保护的
- 泵可以使用功能码0x03或0x04读取传感器数据
- 控制命令必须写入地址16及以上
- SPI和Modbus TCP处理现在完全分离
