# 网络配置说明

## 系统架构

```
泵 (*************) ←→ WiFi AP (USR-W610_A018) ←→ ESP8266传感器 (***********:502)
```

## 网络参数配置

### 泵的WiFi AP配置
- **WiFi名称**: USR-W610_A018
- **密码**: 无密码
- **泵IP地址**: *************
- **网关地址**: ***********
- **子网**: ***********/24

### ESP8266传感器配置
- **工作模式**: Station模式（连接到泵的WiFi）
- **固定IP**: ***********
- **子网掩码**: *************
- **网关**: ***********
- **Modbus TCP端口**: 502

## 通信流程

1. **ESP8266启动** → 连接到泵的WiFi (USR-W610_A018)
2. **设置静态IP** → ***********
3. **启动Modbus TCP服务器** → 监听端口502
4. **泵连接** → 泵通过Modbus TCP协议连接到***********:502
5. **数据交换** → 泵读取传感器数据

## 预期启动日志

```
WiFi connected to pump! IP: ***********
Modbus TCP Sensor Server listening on ***********:502
Pump (*************) can now connect to get sensor data
Sensor ready! Pump can connect to ***********:502 for Modbus TCP
Modbus TCP initialized
```

## 泵连接时的日志

```
Modbus TCP client connected from *************:xxxx
```

## 测试方法

### 1. 从同一网络测试
确保测试设备也连接到USR-W610_A018网络，然后：
```bash
python3 test_modbus.py
```

### 2. 使用Modbus工具测试
- **IP地址**: ***********
- **端口**: 502
- **协议**: Modbus TCP
- **单元ID**: 1

### 3. 网络连通性测试
```bash
ping ***********
telnet *********** 502
```

## 故障排除

### 1. WiFi连接问题
- 确认泵的WiFi AP (USR-W610_A018) 正常工作
- 检查ESP8266是否成功连接到WiFi
- 确认静态IP配置正确

### 2. Modbus连接问题
- 确认ESP8266的IP为***********
- 检查端口502是否正常监听
- 确认泵能够访问***********

### 3. 数据交换问题
- 检查Modbus寄存器地址映射
- 确认STM32与ESP8266的SPI通信正常
- 监控串口调试输出

## 重要提醒

1. **固定IP配置**: ESP8266使用静态IP ***********，不使用DHCP
2. **无密码WiFi**: 泵的WiFi没有密码，直接连接
3. **服务器模式**: ESP8266作为Modbus TCP服务器，等待泵连接
4. **端口502**: 使用标准Modbus TCP端口
5. **网关配置**: 确保网关设置为***********以保证网络通信正常
