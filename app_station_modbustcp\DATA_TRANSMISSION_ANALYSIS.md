# ESP8266 到泵的32字节数据传输分析

## 当前传输状态分析

### 从日志分析
```
Modbus request: TID=122, FC=0x03, Len=12
Modbus response sent: FC=0x03, Len=13
```

### 数据长度分析

#### 泵的请求（12字节）
- MBAP头: 6字节
- 功能码: 1字节 (0x03)
- 起始地址: 2字节
- 寄存器数量: 2字节
- 单元ID: 1字节
- **总计: 12字节**

#### ESP8266的响应（13字节）
- MBAP头: 7字节
- 功能码: 1字节 (0x03)
- 字节计数: 1字节
- 数据: 4字节 (2个寄存器 × 2字节)
- **总计: 13字节**

## 问题分析

### 当前问题
**ESP8266只返回4字节数据，而不是32字节！**

这表明泵只请求了2个寄存器，而不是16个寄存器。

### STM32数据映射
STM32发送8个32位数据（32字节），应该映射到16个16位Modbus寄存器：

```
STM32数据[0] (32位) → 寄存器0(高16位) + 寄存器1(低16位)
STM32数据[1] (32位) → 寄存器2(高16位) + 寄存器3(低16位)
STM32数据[2] (32位) → 寄存器4(高16位) + 寄存器5(低16位)
STM32数据[3] (32位) → 寄存器6(高16位) + 寄存器7(低16位)
STM32数据[4] (32位) → 寄存器8(高16位) + 寄存器9(低16位)
STM32数据[5] (32位) → 寄存器10(高16位) + 寄存器11(低16位)
STM32数据[6] (32位) → 寄存器12(高16位) + 寄存器13(低16位)
STM32数据[7] (32位) → 寄存器14(高16位) + 寄存器15(低16位)
```

## 完整32字节数据传输方案

### 方案1: 泵请求16个寄存器
泵需要发送以下Modbus请求：
- 功能码: 0x03 (读保持寄存器)
- 起始地址: 0
- 寄存器数量: 16
- 预期响应: 39字节 (MBAP头7 + 功能码1 + 字节计数1 + 数据32)

### 方案2: 分批读取
如果泵有限制，可以分批读取：
- 第1次: 读取寄存器0-7 (16字节数据)
- 第2次: 读取寄存器8-15 (16字节数据)

### 方案3: 使用输入寄存器
泵也可以使用功能码0x04读取输入寄存器：
- 功能码: 0x04 (读输入寄存器)
- 起始地址: 0
- 寄存器数量: 16

## 验证方法

### 1. 检查泵的请求参数
从调试日志中查看：
```
Modbus request: TID=X, FC=0x03, Len=X, Addr=X, Qty=X
```
- Addr: 起始地址应该是0
- Qty: 数量应该是16（获取完整32字节）

### 2. 验证ESP8266响应
完整32字节响应应该显示：
```
Modbus response sent: FC=0x03, Len=39, Data=33 bytes
```

### 3. 检查寄存器值
启用调试模式，查看寄存器值是否正确更新：
```
Register values: 0x1234 0x5678 0x9ABC 0xDEF0 ...
```

## 泵配置建议

### Modbus TCP客户端配置
- **IP地址**: ***********
- **端口**: 502
- **单元ID**: 1
- **功能码**: 0x03 (读保持寄存器) 或 0x04 (读输入寄存器)
- **起始地址**: 0
- **寄存器数量**: 16
- **数据类型**: 16位无符号整数
- **字节序**: 大端序（网络字节序）

### 预期数据格式
```
寄存器0-1:   STM32数据[0] = (寄存器0 << 16) | 寄存器1
寄存器2-3:   STM32数据[1] = (寄存器2 << 16) | 寄存器3
寄存器4-5:   STM32数据[2] = (寄存器4 << 16) | 寄存器5
寄存器6-7:   STM32数据[3] = (寄存器6 << 16) | 寄存器7
寄存器8-9:   STM32数据[4] = (寄存器8 << 16) | 寄存器9
寄存器10-11: STM32数据[5] = (寄存器10 << 16) | 寄存器11
寄存器12-13: STM32数据[6] = (寄存器12 << 16) | 寄存器13
寄存器14-15: STM32数据[7] = (寄存器14 << 16) | 寄存器15
```

## 故障排除

### 如果仍然只收到4字节
1. **检查泵的配置**: 确认泵请求16个寄存器而不是2个
2. **检查ESP8266日志**: 查看实际请求的地址和数量
3. **验证STM32数据**: 确认STM32正在发送8个32位数据
4. **检查寄存器映射**: 确认数据正确存储在寄存器0-15中

### 调试命令
启用调试模式查看详细信息：
```c
debugModeOpen = 1; // 通过SPI命令或直接设置
```

## 结论

ESP8266能够完整传输32字节数据，但需要泵正确配置Modbus请求参数。关键是泵必须请求16个连续的寄存器（地址0-15）才能获得完整的STM32数据。
