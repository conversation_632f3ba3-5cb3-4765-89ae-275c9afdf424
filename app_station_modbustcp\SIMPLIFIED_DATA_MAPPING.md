# 简化的数据映射方案

## 新的映射策略

简化的数据映射：直接将STM32[1]的低16位映射到寄存器0，寄存器1保持0x0000。

### 数据映射规则

```c
// 简化映射规则
Register[0] = STM32[1] & 0xFFFF;  // 直接取低16位
Register[1] = 0x0000;             // 固定为0
```

### 具体示例

假设STM32[1] = `0x00017114`：

```
STM32[1] = 0x00017114
         = 0000 0001 0111 0001 0001 0100 (二进制)
         
Register[0] = 0x7114  (低16位，直接使用)
Register[1] = 0x0000  (固定为0)
```

泵读取到：`Register Values: 0x7114 0x0000`

## Raw Response格式

### 期望的Raw Response
```
Raw Response: 00 XX 00 00 00 07 01 03 04 71 14 00 00
                                        ↑  ↑  ↑  ↑
                                     Reg0   Reg1
```

- `71 14`: 寄存器0的值 (0x7114)
- `00 00`: 寄存器1的值 (0x0000)

## 预期调试输出

### 编译烧录后的输出

```
=== SIMPLIFIED DATA MAPPING ===
STM32[0]=0x0000300A, STM32[1]=0x00017114
Direct extraction from STM32[1]:
  STM32[1] low 16 bits: 0x7114 -> Register[0]
  Fixed value:          0x0000 -> Register[1]
Set Register[0] = 0x7114
Set Register[1] = 0x0000
Expected Raw Response data: 71 14 00 00

=== FINAL REGISTER CHECK ===
Register[0] = 0x7114 (STM32[1] low 16 bits)
Register[1] = 0x0000 (fixed to 0x0000)
✓ Simplified mapping SUCCESS
Pump will read: Register Values: 0x7114 0x0000
Raw Response will contain: 71 14 00 00
✓ Register[1] correctly set to 0x0000
```

### 泵读取结果

```
=== ESP8266 RESPONSE ===
Function Code: 0x03
Total Length: 13 bytes
Data Length: 5 bytes
Register Values: 0x7114 0x0000
Raw Response: 00 XX 00 00 00 07 01 03 04 71 14 00 00
```

## 数据变化示例

基于STM32[1]数据的实时变化：

```
STM32[1]=0x00017114 -> Reg[0]=0x7114, Reg[1]=0x0000 -> Raw: 71 14 00 00
STM32[1]=0x00017215 -> Reg[0]=0x7215, Reg[1]=0x0000 -> Raw: 72 15 00 00
STM32[1]=0x00017316 -> Reg[0]=0x7316, Reg[1]=0x0000 -> Raw: 73 16 00 00
STM32[1]=0x00017417 -> Reg[0]=0x7417, Reg[1]=0x0000 -> Raw: 74 17 00 00
STM32[1]=0x00017518 -> Reg[0]=0x7518, Reg[1]=0x0000 -> Raw: 75 18 00 00
```

## 优势

### 1. 简单直接
- 不需要复杂的位重排逻辑
- 直接使用STM32[1]的低16位
- 代码更容易理解和维护

### 2. 数据完整性
- 保持STM32数据的原始格式
- 不会丢失或改变数据位
- 便于调试和验证

### 3. 固定格式
- 寄存器1始终为0x0000
- 泵可以依赖这个固定格式
- 减少数据解析的复杂性

## 数据含义

### Register[0] = 0x7114
这个值直接来自STM32[1]的低16位，可能表示：
- **传感器读数**: 28948 (十进制)
- **温度值**: 289.48°C (如果小数点后2位)
- **压力值**: 2894.8kPa (如果小数点后1位)
- **计数值**: 28948次
- **状态码**: 0x7114

### Register[1] = 0x0000
固定为0，可能用于：
- **状态标志**: 正常状态
- **错误代码**: 无错误
- **保留字段**: 未使用
- **校验位**: 固定值

## 与之前方案的对比

### 之前的复杂映射
```
STM32[1]=0x00017114 -> 位重排 -> 0x4117 -> Raw: 41 17 10 00
```

### 现在的简化映射
```
STM32[1]=0x00017114 -> 直接取低16位 -> 0x7114 -> Raw: 71 14 00 00
```

## 验证步骤

### 步骤1: 编译烧录
```bash
make clean && make && make flash
```

### 步骤2: 观察调试输出
确认看到：
```
=== SIMPLIFIED DATA MAPPING ===
STM32[1] low 16 bits: 0x7114 -> Register[0]
Fixed value: 0x0000 -> Register[1]
```

### 步骤3: 验证泵读取
泵应该读取到：
```
Register Values: 0x7114 0x0000
Raw Response: ... 71 14 00 00
```

### 步骤4: 数据一致性检查
确认：
1. 寄存器0的值等于STM32[1]的低16位
2. 寄存器1始终为0x0000
3. Raw Response格式正确

## 故障排除

### 如果寄存器0值不正确
检查：
1. STM32[1]的数据是否正确接收
2. 低16位提取逻辑是否正确
3. 寄存器设置函数是否正常工作

### 如果寄存器1不为0
检查：
1. 寄存器1的设置逻辑
2. 是否有其他代码修改了寄存器1
3. Modbus寄存器初始化是否正确

## 成功标志

当看到以下输出时，说明简化映射成功：

```
✓ Simplified mapping SUCCESS
Pump will read: Register Values: 0x7114 0x0000
Raw Response will contain: 71 14 00 00
✓ Register[1] correctly set to 0x0000

=== ESP8266 RESPONSE ===
Register Values: 0x7114 0x0000
Raw Response: 00 XX 00 00 00 07 01 03 04 71 14 00 00
```

这表明ESP8266现在使用简化的映射方案，直接传输STM32[1]的低16位数据。
