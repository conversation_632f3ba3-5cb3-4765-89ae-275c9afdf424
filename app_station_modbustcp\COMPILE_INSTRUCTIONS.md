# ESP8266 Modbus TCP 编译指南

## 编译错误修复

我们修复了以下编译错误：

```
modbus_tcp.c:503:9: error: 'for' loop initial declarations are only allowed in C99 mode
         for (uint16 i = 0; i < byte_count/2 && i < 20; i++) {
         ^
```

这个错误是因为ESP8266 SDK使用的是较老的C标准，不支持在for循环中声明变量。我们已经修改了代码，将变量声明移到循环外部。

## 在Linux环境中编译

### 准备工作

1. 确保已安装ESP8266 NONOS SDK和xtensa-lx106-elf工具链
2. 确保工具链在PATH中

### 编译步骤

在Linux终端中执行以下命令：

```bash
cd /mnt/Share/ESP8266_NONOS_SDK/app_station_modbustcp
make clean
make
```

### 烧录步骤

编译成功后，使用以下命令烧录：

```bash
make flash
```

## 常见问题

### 1. 找不到工具链

错误：
```
xtensa-lx106-elf-gcc: command not found
```

解决方法：
```bash
export PATH=$PATH:/path/to/xtensa-lx106-elf/bin
```

### 2. 找不到SDK头文件

错误：
```
fatal error: user_interface.h: No such file or directory
```

解决方法：
检查Makefile中的SDK路径是否正确：
```
SDK_BASE   ?= /path/to/ESP8266_NONOS_SDK
```

### 3. 编译警告

警告：
```
warning: implicit declaration of function 'os_printf'
```

解决方法：
确保包含了正确的头文件：
```c
#include "osapi.h"
```

## 编译成功标志

编译成功后，应该看到类似以下输出：

```
Compile .output/eagle/debug/obj/user_main.o
Compile .output/eagle/debug/obj/modbus_tcp.o
...
LD .output/eagle/debug/lib/libuser.a
...
LD .output/eagle/debug/image/eagle.app.v6.out
...
Generate eagle.flash.bin and eagle.irom0text.bin successully in folder bin.
```

## 在Windows环境中编译

如果需要在Windows环境中编译，建议使用以下方法：

1. **使用WSL (Windows Subsystem for Linux)**：
   ```
   wsl -d Ubuntu
   cd /mnt/d/VM/share/ESP8266_NONOS_SDK/app_station_modbustcp
   make clean && make
   ```

2. **使用Docker**：
   ```
   docker run --rm -v d:/VM/share:/mnt/Share -w /mnt/Share/ESP8266_NONOS_SDK/app_station_modbustcp espressif/esp8266-nonos-sdk make clean && make
   ```

3. **使用虚拟机**：
   在虚拟机中安装Linux，然后挂载共享文件夹进行编译。

## 编译后文件

编译成功后，将在`bin`目录下生成以下文件：

- `eagle.flash.bin`
- `eagle.irom0text.bin`
- `eagle.app.v6.out`

这些文件用于烧录到ESP8266。
