/*
 * ESPRSSIF MIT License
 *
 * Copyright (c) 2016 <ESPRESSIF SYSTEMS (SHANGHAI) PTE LTD>
 *
 * Permission is hereby granted for use on ESPRESSIF SYSTEMS ESP8266 only, in which case,
 * it is free of charge, to any person obtaining a copy of this software and associated
 * documentation files (the "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the Software is furnished
 * to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all copies or
 * substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
 * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
 * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 *
 */

unsigned char default_private_key[] = {
  0x30, 0x82, 0x01, 0x3a, 0x02, 0x01, 0x00, 0x02, 0x41, 0x00, 0xb9, 0x83,
  0x30, 0xca, 0xfb, 0xec, 0x11, 0x9e, 0x94, 0xb7, 0x89, 0xf2, 0x84, 0x2c,
  0xda, 0xe1, 0x9a, 0x53, 0x3a, 0x1b, 0x6e, 0xc9, 0x85, 0x81, 0xf9, 0xa3,
  0x41, 0xdb, 0xe2, 0x82, 0x3b, 0xfa, 0x80, 0x22, 0x3b, 0x81, 0x6d, 0x25,
  0x73, 0x7e, 0xf6, 0x49, 0xcc, 0x69, 0x3c, 0x6c, 0xd8, 0x05, 0xfb, 0x92,
  0x02, 0xcf, 0x19, 0x2a, 0x10, 0x7d, 0x69, 0x7a, 0xd8, 0x9d, 0xd3, 0xcf,
  0x6c, 0xef, 0x02, 0x03, 0x01, 0x00, 0x01, 0x02, 0x40, 0x1d, 0x13, 0x92,
  0xf2, 0x3d, 0xca, 0x22, 0x78, 0xd8, 0x96, 0x6b, 0xe8, 0xb7, 0x0e, 0xd0,
  0xbf, 0xcb, 0x90, 0x7f, 0xeb, 0x0c, 0xd2, 0x49, 0x3a, 0xb6, 0x06, 0x00,
  0xac, 0x96, 0x34, 0x13, 0x72, 0x4b, 0x8c, 0xd2, 0xb9, 0x35, 0xf5, 0x64,
  0x18, 0xb2, 0x47, 0x5b, 0x9f, 0xbb, 0xf2, 0x5b, 0x2f, 0x66, 0x78, 0x2d,
  0x0a, 0x76, 0x44, 0xc5, 0x4f, 0xdb, 0x7d, 0x13, 0xcf, 0xa5, 0x08, 0xdc,
  0x01, 0x02, 0x21, 0x00, 0xdf, 0x9a, 0x89, 0xd0, 0xef, 0x23, 0xcf, 0x12,
  0xac, 0x8a, 0x63, 0x1a, 0x8c, 0xc0, 0x3f, 0xf4, 0x38, 0x52, 0x3c, 0x9f,
  0x19, 0x0a, 0x37, 0xd2, 0xcb, 0x5d, 0xeb, 0xb6, 0x2a, 0x33, 0xb0, 0x91,
  0x02, 0x21, 0x00, 0xd4, 0x63, 0xd9, 0x6a, 0x18, 0x5b, 0xe8, 0xa8, 0x57,
  0x4d, 0xd1, 0x9a, 0xa8, 0xd7, 0xe1, 0x65, 0x75, 0xb3, 0xb9, 0x5c, 0x94,
  0x14, 0xca, 0x98, 0x41, 0x47, 0x9c, 0x0a, 0x22, 0x38, 0x05, 0x7f, 0x02,
  0x20, 0x6a, 0xce, 0xfd, 0xef, 0xe0, 0x9b, 0x61, 0x49, 0x91, 0x43, 0x95,
  0x6d, 0x54, 0x38, 0x6d, 0x14, 0x32, 0x67, 0x0d, 0xf0, 0x0d, 0x5c, 0xf5,
  0x27, 0x6a, 0xdf, 0x55, 0x3d, 0xb1, 0xd0, 0xf9, 0x11, 0x02, 0x21, 0x00,
  0xba, 0x94, 0xa0, 0xf9, 0xb0, 0x3e, 0x85, 0x8b, 0xe5, 0x6e, 0x4a, 0x95,
  0x88, 0x80, 0x65, 0xd5, 0x00, 0xea, 0x8b, 0x0b, 0x46, 0x57, 0x61, 0x87,
  0x11, 0xc9, 0xfb, 0xcd, 0x77, 0x34, 0x29, 0xb7, 0x02, 0x20, 0x06, 0x8d,
  0x41, 0x11, 0x47, 0x93, 0xcb, 0xad, 0xda, 0x5d, 0xe1, 0x9d, 0x49, 0x8d,
  0xe0, 0xab, 0x48, 0xe6, 0x18, 0x28, 0x4a, 0x94, 0xae, 0xf9, 0xad, 0xc5,
  0x5b, 0x0b, 0x15, 0xc6, 0x73, 0x17
};
unsigned int default_private_key_len = 318;
