#ifndef __MODBUS_TCP_H__
#define __MODBUS_TCP_H__

#include "os_type.h"
#include "espconn.h"

// Modbus TCP 标准端口
#define MODBUS_TCP_PORT 502

// Modbus TCP MBAP Header 长度
#define MODBUS_MBAP_HEADER_LENGTH 6

// Modbus PDU 最大长度
#define MODBUS_PDU_MAX_LENGTH 253

// Modbus TCP ADU 最大长度 (MBAP Header + PDU)
#define MODBUS_TCP_ADU_MAX_LENGTH (MODBUS_MBAP_HEADER_LENGTH + MODBUS_PDU_MAX_LENGTH)

// Modbus 功能码定义
#define MODBUS_FC_READ_COILS                0x01
#define MODBUS_FC_READ_DISCRETE_INPUTS      0x02
#define MODBUS_FC_READ_HOLDING_REGISTERS    0x03
#define MODBUS_FC_READ_INPUT_REGISTERS      0x04
#define MODBUS_FC_WRITE_SINGLE_COIL         0x05
#define MODBUS_FC_WRITE_SINGLE_REGISTER     0x06
#define MODBUS_FC_WRITE_MULTIPLE_COILS      0x0F
#define MODBUS_FC_WRITE_MULTIPLE_REGISTERS  0x10

// Modbus 异常码定义
#define MODBUS_EXCEPTION_ILLEGAL_FUNCTION           0x01
#define MODBUS_EXCEPTION_ILLEGAL_DATA_ADDRESS       0x02
#define MODBUS_EXCEPTION_ILLEGAL_DATA_VALUE         0x03
#define MODBUS_EXCEPTION_SLAVE_DEVICE_FAILURE       0x04
#define MODBUS_EXCEPTION_ACKNOWLEDGE                0x05
#define MODBUS_EXCEPTION_SLAVE_DEVICE_BUSY          0x06
#define MODBUS_EXCEPTION_MEMORY_PARITY_ERROR        0x08
#define MODBUS_EXCEPTION_GATEWAY_PATH_UNAVAILABLE   0x0A
#define MODBUS_EXCEPTION_GATEWAY_TARGET_FAILED      0x0B

// Modbus TCP MBAP Header 结构
typedef struct {
    uint16 transaction_id;  // 事务标识符
    uint16 protocol_id;     // 协议标识符 (固定为0)
    uint16 length;          // 长度字段 (单元标识符 + PDU长度)
    uint8  unit_id;         // 单元标识符 (从站地址)
} modbus_mbap_header_t;

// Modbus TCP ADU 结构
typedef struct {
    modbus_mbap_header_t mbap;
    uint8 pdu[MODBUS_PDU_MAX_LENGTH];
    uint16 pdu_length;
} modbus_tcp_adu_t;

// Modbus 寄存器和线圈数据结构
typedef struct {
    uint16 holding_registers[1000];     // 保持寄存器 (40001-41000)
    uint16 input_registers[1000];       // 输入寄存器 (30001-31000)
    uint8  coils[1000];                 // 线圈 (00001-01000)
    uint8  discrete_inputs[1000];       // 离散输入 (10001-11000)
} modbus_data_t;

// 函数声明
void ICACHE_FLASH_ATTR modbus_tcp_init(void);
void ICACHE_FLASH_ATTR modbus_tcp_process_request(void *arg, char *data, unsigned short len);
void ICACHE_FLASH_ATTR modbus_tcp_send_response(struct espconn *conn, modbus_tcp_adu_t *response);
void ICACHE_FLASH_ATTR modbus_tcp_send_exception(struct espconn *conn, uint16 transaction_id, uint8 unit_id, uint8 function_code, uint8 exception_code);

// Modbus 功能处理函数
uint16 ICACHE_FLASH_ATTR modbus_read_coils(uint16 start_addr, uint16 quantity, uint8 *response_data);
uint16 ICACHE_FLASH_ATTR modbus_read_discrete_inputs(uint16 start_addr, uint16 quantity, uint8 *response_data);
uint16 ICACHE_FLASH_ATTR modbus_read_holding_registers(uint16 start_addr, uint16 quantity, uint8 *response_data);
uint16 ICACHE_FLASH_ATTR modbus_read_input_registers(uint16 start_addr, uint16 quantity, uint8 *response_data);
uint8 ICACHE_FLASH_ATTR modbus_write_single_coil(uint16 addr, uint16 value);
uint8 ICACHE_FLASH_ATTR modbus_write_single_register(uint16 addr, uint16 value);
uint8 ICACHE_FLASH_ATTR modbus_write_multiple_coils(uint16 start_addr, uint16 quantity, uint8 *data);
uint8 ICACHE_FLASH_ATTR modbus_write_multiple_registers(uint16 start_addr, uint16 quantity, uint8 *data);

// 数据访问函数
void ICACHE_FLASH_ATTR modbus_data_init(void);
uint16 ICACHE_FLASH_ATTR modbus_get_holding_register(uint16 addr);
void ICACHE_FLASH_ATTR modbus_set_holding_register(uint16 addr, uint16 value);
uint16 ICACHE_FLASH_ATTR modbus_get_input_register(uint16 addr);
void ICACHE_FLASH_ATTR modbus_set_input_register(uint16 addr, uint16 value);
uint8 ICACHE_FLASH_ATTR modbus_get_coil(uint16 addr);
void ICACHE_FLASH_ATTR modbus_set_coil(uint16 addr, uint8 value);
uint8 ICACHE_FLASH_ATTR modbus_get_discrete_input(uint16 addr);
void ICACHE_FLASH_ATTR modbus_set_discrete_input(uint16 addr, uint8 value);

// 工具函数
uint16 ICACHE_FLASH_ATTR modbus_bytes_to_uint16(uint8 *bytes);
void ICACHE_FLASH_ATTR modbus_uint16_to_bytes(uint16 value, uint8 *bytes);

#endif // __MODBUS_TCP_H__
